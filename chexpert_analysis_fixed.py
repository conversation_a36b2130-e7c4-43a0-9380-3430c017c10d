from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import re
import pandas as pd
import numpy as np
import os
import warnings
from datasets import load_dataset
warnings.filterwarnings("ignore", category=UserWarning, module="tqdm")

def extract_patient_id(path):
    """Extract patient ID from path"""
    # Use regex to extract patient ID, e.g., extract "patient00001" from "CheXpert-v1.0-small/train/patient00001/study1/view1_frontal.jpg"
    match = re.search(r'patient(\d+)', path)
    if match:
        return f"patient{match.group(1)}"
    return None

def load_chexpert_data_from_datasets():
    """
    Load CheXpert dataset using datasets library
    
    Returns:
        combined_df: Combined DataFrame
    """
    print("Loading CheXpert dataset using datasets library...")
    
    # Load dataset using datasets library
    ds = load_dataset("danjacobellis/chexpert")
    
    # Check available splits
    print("Available splits:", list(ds.keys()))
    
    # Convert to pandas DataFrames
    if 'train' not in ds:
        raise ValueError(f"No 'train' split found in dataset. Available splits: {list(ds.keys())}")
    
    train_df = ds['train'].to_pandas()
    print(f"Training data loaded: {len(train_df)} samples")
    
    if len(train_df) == 0:
        raise ValueError("Training dataset is empty!")
    
    # Try different validation split names
    valid_df = None
    validation_candidates = ['validation', 'val', 'valid', 'test']
    
    for candidate in validation_candidates:
        if candidate in ds:
            valid_df = ds[candidate].to_pandas()
            print(f"Using '{candidate}' as validation set: {len(valid_df)} samples")
            break
    
    if valid_df is None:
        # If no validation split found, create one from training data
        print("No validation split found. Creating 80/20 split from training data...")
        train_df, valid_df = train_test_split(train_df, test_size=0.2, random_state=42)
        print(f"Split created - Train: {len(train_df)}, Val: {len(valid_df)}")
    
    # Add data source identifier
    train_df['original_split'] = 'train'
    valid_df['original_split'] = 'valid'
    
    # Combine data
    combined_df = pd.concat([train_df, valid_df], ignore_index=True)
    
    print(f"Total loaded records: {len(combined_df)}")
    print(f"Original training records: {len(train_df)}")
    print(f"Original validation records: {len(valid_df)}")
    
    return combined_df

def filter_frontal_views(df):
    """
    Keep only frontal view images

    Args:
        df: Original DataFrame

    Returns:
        filtered_df: Filtered DataFrame
    """
    print("\nFiltering frontal views...")

    # Check what values exist in Frontal/Lateral column
    print("Checking Frontal/Lateral column values:")
    if 'Frontal/Lateral' in df.columns:
        print("Unique values in 'Frontal/Lateral':", df['Frontal/Lateral'].unique())
        print("Value counts:", df['Frontal/Lateral'].value_counts())
    else:
        print("Available columns:", list(df.columns))
        # Look for similar column names
        frontal_cols = [col for col in df.columns if 'frontal' in col.lower() or 'lateral' in col.lower()]
        print("Columns containing 'frontal' or 'lateral':", frontal_cols)

        # Also check for view-related columns
        view_cols = [col for col in df.columns if 'view' in col.lower()]
        print("Columns containing 'view':", view_cols)

        return df  # Return original df if column not found

    # Filter frontal views - try different possible values
    possible_frontal_values = ['Frontal', 'frontal', 'FRONTAL', 'PA', 'AP']
    frontal_df = None

    for value in possible_frontal_values:
        if value in df['Frontal/Lateral'].values:
            frontal_df = df[df['Frontal/Lateral'] == value].copy()
            print(f"Found frontal views with value '{value}': {len(frontal_df)} records")
            break

    if frontal_df is None or len(frontal_df) == 0:
        print("Warning: No frontal views found with standard values. Returning all data.")
        frontal_df = df.copy()

    print(f"Original records: {len(df)}")
    print(f"Frontal view records: {len(frontal_df)}")
    print(f"Filtered lateral views: {len(df) - len(frontal_df)}")

    return frontal_df

def create_patient_level_split(df, test_size=0.2, random_state=42):
    """
    Create patient-level train/validation split
    
    Args:
        df: DataFrame containing image data
        test_size: Validation set proportion
        random_state: Random seed
    
    Returns:
        train_df, val_df: Training and validation DataFrames
    """
    print(f"\nCreating patient-level {int((1-test_size)*100)}/{int(test_size*100)} split...")
    
    # Extract patient IDs
    df['patient_id'] = df['Path'].apply(extract_patient_id)
    
    # Get unique patient IDs
    unique_patients = df['patient_id'].unique()
    print(f"Number of unique patients: {len(unique_patients)}")
    
    # Split by patient ID
    train_patients, val_patients = train_test_split(
        unique_patients, 
        test_size=test_size, 
        random_state=random_state
    )
    
    print(f"Training patients: {len(train_patients)}")
    print(f"Validation patients: {len(val_patients)}")
    
    # Split data based on patient IDs
    train_df = df[df['patient_id'].isin(train_patients)].copy()
    val_df = df[df['patient_id'].isin(val_patients)].copy()
    
    print(f"Training images: {len(train_df)}")
    print(f"Validation images: {len(val_df)}")
    
    # Verify no patient overlap
    train_patient_set = set(train_df['patient_id'].unique())
    val_patient_set = set(val_df['patient_id'].unique())
    overlap = train_patient_set.intersection(val_patient_set)
    
    if len(overlap) > 0:
        print(f"Warning: Found overlapping patients: {overlap}")
    else:
        print("Verification passed: No patient overlap between training and validation sets")
    
    return train_df, val_df

if __name__ == "__main__":
    try:
        # Load data
        df = load_chexpert_data_from_datasets()
        
        # Filter frontal views
        frontal_df = filter_frontal_views(df)
        
        # Create patient-level split
        train_df, val_df = create_patient_level_split(frontal_df, test_size=0.2)
        
        print("\n" + "="*50)
        print("SUCCESS: Data loaded and processed successfully!")
        print("="*50)
        print(f"Final training set: {len(train_df)} images")
        print(f"Final validation set: {len(val_df)} images")
        
    except Exception as e:
        print(f"\nERROR: {str(e)}")
        print("Please check the dataset and try again.")
