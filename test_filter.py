import pandas as pd
import numpy as np

# Create test data to simulate the issue
test_data = {
    'Frontal/Lateral': [0, 1, 0, 1, 0, 0, 1],
    'Path': ['path1', 'path2', 'path3', 'path4', 'path5', 'path6', 'path7']
}

df = pd.DataFrame(test_data)

def filter_frontal_views(df):
    """
    Keep only frontal view images
    
    Args:
        df: Original DataFrame
    
    Returns:
        filtered_df: Filtered DataFrame
    """
    print("\nFiltering frontal views...")
    
    # Filter frontal views (0=Frontal, 1=Lateral)
    frontal_df = df[df['Frontal/Lateral'] == 0].copy()
    
    print(f"Original records: {len(df)}")
    print(f"Frontal view records: {len(frontal_df)}")
    print(f"Filtered lateral views: {len(df) - len(frontal_df)}")
    
    return frontal_df

# Test the function
print("Test data:")
print(df)
print("\nUnique values in 'Frontal/Lateral':", df['Frontal/Lateral'].unique())
print("Value counts:", df['Frontal/Lateral'].value_counts())

frontal_df = filter_frontal_views(df)
print("\nFiltered data:")
print(frontal_df)
