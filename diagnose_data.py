from datasets import load_dataset
import pandas as pd
import warnings
warnings.filterwarnings("ignore", category=UserWarning, module="tqdm")

def diagnose_frontal_lateral_column():
    """诊断 Frontal/Lateral 列的问题"""
    print("Loading dataset for diagnosis...")
    
    # Load dataset
    ds = load_dataset("danjacobellis/chexpert")
    print("Available splits:", list(ds.keys()))
    
    # Get a sample of data
    train_df = ds['train'].to_pandas()
    print(f"Total records: {len(train_df)}")
    
    # Check column names
    print("\nAll column names:")
    for i, col in enumerate(train_df.columns):
        print(f"{i}: '{col}' (type: {type(col)})")
    
    # Focus on Frontal/Lateral column
    frontal_col = 'Frontal/Lateral'
    if frontal_col in train_df.columns:
        print(f"\n=== Analysis of '{frontal_col}' column ===")
        
        # Check data type
        print(f"Column data type: {train_df[frontal_col].dtype}")
        
        # Check unique values
        unique_vals = train_df[frontal_col].unique()
        print(f"Unique values: {unique_vals}")
        print(f"Unique values types: {[type(v) for v in unique_vals]}")
        
        # Check value counts
        print(f"Value counts:")
        print(train_df[frontal_col].value_counts())
        
        # Check for null values
        print(f"Null values: {train_df[frontal_col].isnull().sum()}")
        
        # Test different filtering approaches
        print(f"\n=== Testing different filters ===")
        
        # Test with integer 0
        test1 = train_df[train_df[frontal_col] == 0]
        print(f"Filter with int 0: {len(test1)} records")
        
        # Test with string '0'
        test2 = train_df[train_df[frontal_col] == '0']
        print(f"Filter with string '0': {len(test2)} records")
        
        # Test with float 0.0
        test3 = train_df[train_df[frontal_col] == 0.0]
        print(f"Filter with float 0.0: {len(test3)} records")
        
        # Show first few values
        print(f"\nFirst 10 values in column:")
        for i in range(min(10, len(train_df))):
            val = train_df[frontal_col].iloc[i]
            print(f"  {i}: {repr(val)} (type: {type(val)})")
            
    else:
        print(f"Column '{frontal_col}' not found!")
        # Look for similar columns
        similar_cols = [col for col in train_df.columns if 'frontal' in col.lower() or 'lateral' in col.lower()]
        print(f"Similar columns: {similar_cols}")

if __name__ == "__main__":
    diagnose_frontal_lateral_column()
