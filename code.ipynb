from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import re
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import os
import warnings
from datasets import load_dataset
warnings.filterwarnings("ignore", category=UserWarning, module="tqdm")

def extract_patient_id(path):
    """Extract patient ID from path"""
    # Use regex to extract patient ID, e.g., extract "patient00001" from "CheXpert-v1.0-small/train/patient00001/study1/view1_frontal.jpg"
    match = re.search(r'patient(\d+)', path)
    if match:
        return f"patient{match.group(1)}"
    return None

def load_chexpert_data_from_datasets():
    """
    Load CheXpert dataset using datasets library
    
    Returns:
        combined_df: Combined DataFrame
    """
    print("Loading CheXpert dataset using datasets library...")
    
    # Load dataset using datasets library
    ds = load_dataset("danjacobellis/chexpert")
    
    # Convert to pandas DataFrames
    train_df = ds['train'].to_pandas()
    valid_df = ds['valid'].to_pandas()
    
    # Add data source identifier
    train_df['original_split'] = 'train'
    valid_df['original_split'] = 'valid'
    
    # Combine data
    combined_df = pd.concat([train_df, valid_df], ignore_index=True)
    
    print(f"Total loaded records: {len(combined_df)}")
    print(f"Original training records: {len(train_df)}")
    print(f"Original validation records: {len(valid_df)}")
    
    return combined_df


df = load_chexpert_data_from_datasets()


def filter_frontal_views(df):
    """
    Keep only frontal view images
    
    Args:
        df: Original DataFrame
    
    Returns:
        filtered_df: Filtered DataFrame
    """
    print("\nFiltering frontal views...")
    
    # Filter frontal views (0=Frontal, 1=Lateral)
    frontal_df = df[df['Frontal/Lateral'] == 0].copy()
    print(f"df['Frontal/Lateral']）
    print(f"Original records: {len(df)}")
    print(f"Frontal view records: {len(frontal_df)}")
    print(f"Filtered lateral views: {len(df) - len(frontal_df)}")
    
    return frontal_df
frontal_df = filter_frontal_views(df)

def create_patient_level_split(df, test_size=0.2, random_state=42):
    """
    Create patient-level train/validation split
    
    Args:
        df: DataFrame containing image data
        test_size: Validation set proportion
        random_state: Random seed
    
    Returns:
        train_df, val_df: Training and validation DataFrames
    """
    print(f"\nCreating patient-level {int((1-test_size)*100)}/{int(test_size*100)} split...")
    
    # Extract patient IDs
    df['patient_id'] = df['Path'].apply(extract_patient_id)
    
    # Get unique patient IDs
    unique_patients = df['patient_id'].unique()
    print(f"Number of unique patients: {len(unique_patients)}")
    
    # Split by patient ID
    train_patients, val_patients = train_test_split(
        unique_patients, 
        test_size=test_size, 
        random_state=random_state
    )
    
    print(f"Training patients: {len(train_patients)}")
    print(f"Validation patients: {len(val_patients)}")
    
    # Split data based on patient IDs
    train_df = df[df['patient_id'].isin(train_patients)].copy()
    val_df = df[df['patient_id'].isin(val_patients)].copy()
    
    print(f"Training images: {len(train_df)}")
    print(f"Validation images: {len(val_df)}")
    
    # Verify no patient overlap
    train_patient_set = set(train_df['patient_id'].unique())
    val_patient_set = set(val_df['patient_id'].unique())
    overlap = train_patient_set.intersection(val_patient_set)
    
    if len(overlap) > 0:
        print(f"Warning: Found overlapping patients: {overlap}")
    else:
        print("Verification passed: No patient overlap between training and validation sets")
    
    return train_df, val_df
train_df, val_df = create_patient_level_split(frontal_df, test_size=0.2)

def analyze_dataset_statistics(train_df, val_df):
    """
    Analyze dataset statistics
    
    Args:
        train_df: Training DataFrame
        val_df: Validation DataFrame
    """
    print("\n" + "="*50)
    print("Dataset Statistical Analysis")
    print("="*50)
    
    # Basic statistics
    print(f"Training set: {len(train_df)} images, {len(train_df['patient_id'].unique())} patients")
    print(f"Validation set: {len(val_df)} images, {len(val_df['patient_id'].unique())} patients")
    
    # Gender distribution
    print("\nGender distribution:")
    print("Training set:")
    print(train_df['Sex'].value_counts())
    print("Validation set:")
    print(val_df['Sex'].value_counts())
    
    # Age distribution
    print(f"\nAge distribution:")
    print(f"Training set age: mean {train_df['Age'].mean():.1f} ± {train_df['Age'].std():.1f}")
    print(f"Validation set age: mean {val_df['Age'].mean():.1f} ± {val_df['Age'].std():.1f}")
    
    # AP/PA distribution
    print("\nAP/PA distribution:")
    print("Training set:")
    print(train_df['AP/PA'].value_counts())
    print("Validation set:")
    print(val_df['AP/PA'].value_counts())
analyze_dataset_statistics(train_df, val_df)

def save_splits(train_df, val_df, output_dir="chexpert_splits"):
    """
    Save split datasets
    
    Args:
        train_df: Training DataFrame
        val_df: Validation DataFrame
        output_dir: Output directory
    """
    print(f"\nSaving split results to {output_dir}/...")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Save CSV files
    train_df.to_csv(os.path.join(output_dir, "train_frontal.csv"), index=False)
    val_df.to_csv(os.path.join(output_dir, "val_frontal.csv"), index=False)
    
    # Save patient ID lists
    train_patients = sorted(train_df['patient_id'].unique())
    val_patients = sorted(val_df['patient_id'].unique())
    
    with open(os.path.join(output_dir, "train_patients.txt"), 'w') as f:
        f.write('\n'.join(train_patients))
    
    with open(os.path.join(output_dir, "val_patients.txt"), 'w') as f:
        f.write('\n'.join(val_patients))
    
    print(f"  - train_frontal.csv: {len(train_df)} records")
    print(f"  - val_frontal.csv: {len(val_df)} records")
    print(f"  - train_patients.txt: {len(train_patients)} patients")
    print(f"  - val_patients.txt: {len(val_patients)} patients")
save_splits(train_df, val_df)

class CheXpertAnalyzer:
    """CheXpert Dataset Analyzer"""
    
    def __init__(self, train_df=None, val_df=None, target_pathology="Pleural Effusion"):
        """
        Initialize the analyzer
        
        Args:
            train_df: Training DataFrame (if None, will load from datasets)
            val_df: Validation DataFrame (if None, will load from datasets)
            target_pathology: Target pathology label
        """
        self.target_pathology = target_pathology
        
        if train_df is not None and val_df is not None:
            # Use provided DataFrames
            self.train_df = train_df
            self.val_df = val_df
            self.combined_df = pd.concat([self.train_df, self.val_df], ignore_index=True)
            print("Using provided train/val DataFrames")
        else:
            # Fallback to loading data
            print("Loading CheXpert dataset using datasets library...")
            ds = load_dataset("danjacobellis/chexpert")
            self.train_df = ds['train'].to_pandas()
            self.val_df = ds['valid'].to_pandas()
            self.combined_df = pd.concat([self.train_df, self.val_df], ignore_index=True)
        
        # Define all pathology labels
        self.pathology_labels = [
            'No Finding', 'Enlarged Cardiomediastinum', 'Cardiomegaly',
            'Lung Opacity', 'Lung Lesion', 'Edema', 'Consolidation',
            'Pneumonia', 'Atelectasis', 'Pneumothorax', 'Pleural Effusion',
            'Pleural Other', 'Fracture', 'Support Devices'
        ]
        
        print(f"Target pathology for analysis: {self.target_pathology}")
        print(f"Training samples: {len(self.train_df)}")
        print(f"Validation samples: {len(self.val_df)}")
        print(f"Total samples: {len(self.combined_df)}")

    
    def analyze_label_prevalence(self):
        """
        Analyze label prevalence (positive/negative/uncertain/unlabeled)
        
        Returns:
            dict: Dictionary containing counts and proportions for each category
        """
        print(f"\n=== {self.target_pathology} Label Prevalence Analysis ===")
        
        target_column = self.combined_df[self.target_pathology]
        
        # Calculate counts for each category
        positive = (target_column == 1.0).sum()
        negative = (target_column == 0.0).sum()
        uncertain = (target_column == -1.0).sum()
        unlabeled = target_column.isna().sum()
        total = len(target_column)
        
        prevalence = {
            'positive': positive,
            'negative': negative,
            'uncertain': uncertain,
            'unlabeled': unlabeled,
            'total': total,
            'positive_rate': positive / total * 100,
            'negative_rate': negative / total * 100,
            'uncertain_rate': uncertain / total * 100,
            'unlabeled_rate': unlabeled / total * 100
        }
        
        print(f"Positive: {positive:,} ({prevalence['positive_rate']:.2f}%)")
        print(f"Negative: {negative:,} ({prevalence['negative_rate']:.2f}%)")
        print(f"Uncertain: {uncertain:,} ({prevalence['uncertain_rate']:.2f}%)")
        print(f"Unlabeled: {unlabeled:,} ({prevalence['unlabeled_rate']:.2f}%)")
        
        return prevalence
    
    def plot_label_prevalence(self, prevalence):
        """
        Plot label prevalence charts
        
        Args:
            prevalence: Label prevalence dictionary
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Pie chart
        labels = ['Positive', 'Negative', 'Uncertain', 'Unlabeled']
        sizes = [prevalence['positive'], prevalence['negative'], 
                prevalence['uncertain'], prevalence['unlabeled']]
        colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99']
        
        # Only show non-zero categories
        non_zero_indices = [i for i, size in enumerate(sizes) if size > 0]
        labels_filtered = [labels[i] for i in non_zero_indices]
        sizes_filtered = [sizes[i] for i in non_zero_indices]
        colors_filtered = [colors[i] for i in non_zero_indices]
        
        ax1.pie(sizes_filtered, labels=labels_filtered, colors=colors_filtered, 
                autopct='%1.1f%%', startangle=90)
        ax1.set_title(f'{self.target_pathology} Label Distribution')
        
        # Bar chart
        ax2.bar(labels_filtered, sizes_filtered, color=colors_filtered)
        ax2.set_title(f'{self.target_pathology} Label Counts')
        ax2.set_ylabel('Number of Samples')
        ax2.tick_params(axis='x', rotation=45)
        
        # Add value labels
        for i, v in enumerate(sizes_filtered):
            ax2.text(i, v + max(sizes_filtered) * 0.01, f'{v:,}', 
                    ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig(f'{self.target_pathology.replace(" ", "_")}_prevalence.png', 
                   dpi=300, bbox_inches='tight')
        plt.show()
    
    def analyze_cooccurrence(self, second_pathology="Cardiomegaly"):
        """
        Analyze co-occurrence with "No Finding" and a second pathology
        
        Args:
            second_pathology: Second pathology label
        
        Returns:
            dict: Co-occurrence analysis results
        """
        print(f"\n=== {self.target_pathology} Co-occurrence Analysis ===")
        
        # Co-occurrence with "No Finding"
        no_finding_cooccur = self._analyze_pairwise_cooccurrence(
            self.target_pathology, "No Finding"
        )
        
        # Co-occurrence with second pathology
        second_cooccur = self._analyze_pairwise_cooccurrence(
            self.target_pathology, second_pathology
        )
        
        return {
            'no_finding': no_finding_cooccur,
            'second_pathology': second_cooccur,
            'second_pathology_name': second_pathology
        }
    
    def _analyze_pairwise_cooccurrence(self, pathology1, pathology2):
        """
        Analyze pairwise co-occurrence between two pathology labels
        
        Args:
            pathology1: First pathology label
            pathology2: Second pathology label
        
        Returns:
            dict: Co-occurrence statistics
        """
        # Only consider samples with definitive labels (1 or 0) for both pathologies
        mask = (self.combined_df[pathology1].isin([0.0, 1.0])) & \
               (self.combined_df[pathology2].isin([0.0, 1.0]))
        
        subset = self.combined_df[mask]
        
        if len(subset) == 0:
            return {'error': 'No samples with both pathologies labeled'}
        
        # Calculate co-occurrence matrix
        both_positive = ((subset[pathology1] == 1.0) & (subset[pathology2] == 1.0)).sum()
        path1_pos_path2_neg = ((subset[pathology1] == 1.0) & (subset[pathology2] == 0.0)).sum()
        path1_neg_path2_pos = ((subset[pathology1] == 0.0) & (subset[pathology2] == 1.0)).sum()
        both_negative = ((subset[pathology1] == 0.0) & (subset[pathology2] == 0.0)).sum()
        
        total_valid = len(subset)
        
        result = {
            'pathology1': pathology1,
            'pathology2': pathology2,
            'both_positive': both_positive,
            'path1_pos_path2_neg': path1_pos_path2_neg,
            'path1_neg_path2_pos': path1_neg_path2_pos,
            'both_negative': both_negative,
            'total_valid': total_valid,
            'cooccurrence_rate': both_positive / total_valid * 100 if total_valid > 0 else 0
        }
        
        print(f"\n{pathology1} vs {pathology2} Co-occurrence Analysis:")
        print(f"Valid samples: {total_valid:,}")
        print(f"Both positive: {both_positive:,} ({both_positive/total_valid*100:.2f}%)")
        print(f"{pathology1} positive, {pathology2} negative: {path1_pos_path2_neg:,}")
        print(f"{pathology1} negative, {pathology2} positive: {path1_neg_path2_pos:,}")
        print(f"Both negative: {both_negative:,} ({both_negative/total_valid*100:.2f}%)")
        
        return result
    
    def plot_cooccurrence_matrix(self, cooccurrence_data):
        """
        Plot co-occurrence matrix heatmaps
        
        Args:
            cooccurrence_data: Co-occurrence analysis data
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # No Finding co-occurrence matrix
        no_finding_data = cooccurrence_data['no_finding']
        if 'error' not in no_finding_data:
            matrix1 = np.array([
                [no_finding_data['both_negative'], no_finding_data['path1_neg_path2_pos']],
                [no_finding_data['path1_pos_path2_neg'], no_finding_data['both_positive']]
            ])
            
            sns.heatmap(matrix1, annot=True, fmt='d', cmap='Blues', ax=ax1,
                       xticklabels=['No Finding-', 'No Finding+'],
                       yticklabels=[f'{self.target_pathology}-', f'{self.target_pathology}+'])
            ax1.set_title(f'{self.target_pathology} vs No Finding')
        
        # Second pathology co-occurrence matrix
        second_data = cooccurrence_data['second_pathology']
        if 'error' not in second_data:
            matrix2 = np.array([
                [second_data['both_negative'], second_data['path1_neg_path2_pos']],
                [second_data['path1_pos_path2_neg'], second_data['both_positive']]
            ])
            
            second_name = cooccurrence_data['second_pathology_name']
            sns.heatmap(matrix2, annot=True, fmt='d', cmap='Reds', ax=ax2,
                       xticklabels=[f'{second_name}-', f'{second_name}+'],
                       yticklabels=[f'{self.target_pathology}-', f'{self.target_pathology}+'])
            ax2.set_title(f'{self.target_pathology} vs {second_name}')
        
        plt.tight_layout()
        plt.savefig(f'{self.target_pathology.replace(" ", "_")}_cooccurrence.png', 
                   dpi=300, bbox_inches='tight')
        plt.show()
    
    def analyze_distribution_by_demographics(self):
        """
        Analyze distribution by view type (AP vs PA) and sex/age
        
        Returns:
            dict: Demographic distribution analysis results
        """
        print(f"\n=== {self.target_pathology} Demographic Distribution Analysis ===")
        
        # Handle AP/PA mapping (assuming 0=AP, 1=PA based on HF page warning)
        df_copy = self.combined_df.copy()
        
        # Create numeric mapping for AP/PA
        ap_pa_mapping = {'AP': 0, 'PA': 1}
        df_copy['AP_PA_numeric'] = df_copy['AP/PA'].map(ap_pa_mapping)
        
        print("Note: Based on HuggingFace page warning, assuming AP/PA mapping: 0=AP, 1=PA")
        
        # Only analyze samples with definitive labels
        valid_samples = df_copy[df_copy[self.target_pathology].isin([0.0, 1.0])]
        positive_samples = valid_samples[valid_samples[self.target_pathology] == 1.0]
        
        results = {}
        
        # Distribution by AP/PA
        print(f"\nDistribution by View Type:")
        ap_pa_dist = self._analyze_categorical_distribution(
            valid_samples, positive_samples, 'AP/PA'
        )
        results['ap_pa'] = ap_pa_dist
        
        # Distribution by sex
        print(f"\nDistribution by Sex:")
        sex_dist = self._analyze_categorical_distribution(
            valid_samples, positive_samples, 'Sex'
        )
        results['sex'] = sex_dist
        
        # Distribution by age
        print(f"\nDistribution by Age:")
        age_dist = self._analyze_age_distribution(valid_samples, positive_samples)
        results['age'] = age_dist
        
        return results
    
    def _analyze_categorical_distribution(self, valid_samples, positive_samples, column):
        """
        Analyze distribution of categorical variables
        
        Args:
            valid_samples: Valid samples
            positive_samples: Positive samples
            column: Column name to analyze
        
        Returns:
            dict: Distribution statistics
        """
        total_counts = valid_samples[column].value_counts()
        positive_counts = positive_samples[column].value_counts()
        
        result = {}
        for category in total_counts.index:
            total = total_counts[category]
            positive = positive_counts.get(category, 0)
            prevalence = positive / total * 100 if total > 0 else 0
            
            result[category] = {
                'total': total,
                'positive': positive,
                'prevalence': prevalence
            }
            
            print(f"{category}: {positive}/{total} ({prevalence:.2f}%)")
        
        return result
    
    def _analyze_age_distribution(self, valid_samples, positive_samples):
        """
        Analyze age distribution
        
        Args:
            valid_samples: Valid samples
            positive_samples: Positive samples
        
        Returns:
            dict: Age distribution statistics
        """
        # Create age groups
        age_bins = [0, 30, 50, 70, 100]
        age_labels = ['<30', '30-50', '50-70', '70+']
        
        valid_samples_copy = valid_samples.copy()
        positive_samples_copy = positive_samples.copy()
        
        valid_samples_copy['age_group'] = pd.cut(
            valid_samples_copy['Age'], bins=age_bins, labels=age_labels, right=False
        )
        positive_samples_copy['age_group'] = pd.cut(
            positive_samples_copy['Age'], bins=age_bins, labels=age_labels, right=False
        )
        
        age_dist = self._analyze_categorical_distribution(
            valid_samples_copy, positive_samples_copy, 'age_group'
        )
        
        # Add continuous age statistics
        age_stats = {
            'all_mean': valid_samples['Age'].mean(),
            'all_std': valid_samples['Age'].std(),
            'positive_mean': positive_samples['Age'].mean(),
            'positive_std': positive_samples['Age'].std()
        }
        
        print(f"All samples age: {age_stats['all_mean']:.1f} ± {age_stats['all_std']:.1f}")
        print(f"Positive samples age: {age_stats['positive_mean']:.1f} ± {age_stats['positive_std']:.1f}")
        
        return {'groups': age_dist, 'stats': age_stats}

    def plot_demographic_distributions(self, demo_results):
        """
        Plot demographic distribution charts

        Args:
            demo_results: Demographic distribution analysis results
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # AP/PA distribution
        ap_pa_data = demo_results['ap_pa']
        categories = list(ap_pa_data.keys())
        prevalences = [ap_pa_data[cat]['prevalence'] for cat in categories]
        totals = [ap_pa_data[cat]['total'] for cat in categories]

        ax1 = axes[0, 0]
        bars1 = ax1.bar(categories, prevalences, color=['skyblue', 'lightcoral'])
        ax1.set_title(f'{self.target_pathology} Prevalence by View Type')
        ax1.set_ylabel('Prevalence (%)')
        ax1.set_xlabel('View Type (Assumed: 0=AP, 1=PA)')

        # Add sample count labels
        for i, (bar, total) in enumerate(zip(bars1, totals)):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'n={total:,}', ha='center', va='bottom', fontsize=10)

        # Sex distribution
        sex_data = demo_results['sex']
        sex_categories = list(sex_data.keys())
        sex_prevalences = [sex_data[cat]['prevalence'] for cat in sex_categories]
        sex_totals = [sex_data[cat]['total'] for cat in sex_categories]

        ax2 = axes[0, 1]
        bars2 = ax2.bar(sex_categories, sex_prevalences, color=['lightgreen', 'orange'])
        ax2.set_title(f'{self.target_pathology} Prevalence by Sex')
        ax2.set_ylabel('Prevalence (%)')
        ax2.set_xlabel('Sex')

        for i, (bar, total) in enumerate(zip(bars2, sex_totals)):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'n={total:,}', ha='center', va='bottom', fontsize=10)

        # Age group distribution
        age_data = demo_results['age']['groups']
        age_categories = list(age_data.keys())
        age_prevalences = [age_data[cat]['prevalence'] for cat in age_categories]
        age_totals = [age_data[cat]['total'] for cat in age_categories]

        ax3 = axes[1, 0]
        bars3 = ax3.bar(age_categories, age_prevalences, color='mediumpurple')
        ax3.set_title(f'{self.target_pathology} Prevalence by Age Group')
        ax3.set_ylabel('Prevalence (%)')
        ax3.set_xlabel('Age Group')

        for i, (bar, total) in enumerate(zip(bars3, age_totals)):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'n={total:,}', ha='center', va='bottom', fontsize=10)

        # Age distribution histogram
        ax4 = axes[1, 1]
        valid_samples = self.combined_df[self.combined_df[self.target_pathology].isin([0.0, 1.0])]
        positive_samples = valid_samples[valid_samples[self.target_pathology] == 1.0]
        negative_samples = valid_samples[valid_samples[self.target_pathology] == 0.0]

        ax4.hist(negative_samples['Age'], bins=20, alpha=0.7, label='Negative', color='lightblue')
        ax4.hist(positive_samples['Age'], bins=20, alpha=0.7, label='Positive', color='lightcoral')
        ax4.set_title(f'{self.target_pathology} Age Distribution')
        ax4.set_xlabel('Age')
        ax4.set_ylabel('Number of Samples')
        ax4.legend()

        plt.tight_layout()
        plt.savefig(f'{self.target_pathology.replace(" ", "_")}_demographics.png',
                   dpi=300, bbox_inches='tight')
        plt.show()

    def calculate_class_priors_and_baseline(self):
        """
        Calculate class prior probabilities and majority class baseline error rate

        Returns:
            dict: Dictionary containing prior probabilities and baseline error rate
        """
        print(f"\n=== {self.target_pathology} Class Priors and Baseline Analysis ===")

        # Only consider samples with definitive labels (0 or 1)
        valid_samples = self.combined_df[self.combined_df[self.target_pathology].isin([0.0, 1.0])]

        if len(valid_samples) == 0:
            print("Error: No valid labeled samples")
            return None

        positive_count = (valid_samples[self.target_pathology] == 1.0).sum()
        negative_count = (valid_samples[self.target_pathology] == 0.0).sum()
        total_valid = len(valid_samples)

        # Calculate prior probabilities
        positive_prior = positive_count / total_valid
        negative_prior = negative_count / total_valid

        # Calculate majority class baseline error rate (0-1 loss)
        majority_class = 1.0 if positive_count > negative_count else 0.0
        minority_count = min(positive_count, negative_count)
        baseline_error_rate = minority_count / total_valid
        baseline_accuracy = 1 - baseline_error_rate

        results = {
            'total_valid_samples': total_valid,
            'positive_count': positive_count,
            'negative_count': negative_count,
            'positive_prior': positive_prior,
            'negative_prior': negative_prior,
            'majority_class': majority_class,
            'baseline_accuracy': baseline_accuracy,
            'baseline_error_rate': baseline_error_rate,
            'class_imbalance_ratio': max(positive_count, negative_count) / min(positive_count, negative_count)
        }

        print(f"Total valid samples: {total_valid:,}")
        print(f"Positive samples: {positive_count:,} (prior: {positive_prior:.4f})")
        print(f"Negative samples: {negative_count:,} (prior: {negative_prior:.4f})")
        print(f"Majority class: {'Positive' if majority_class == 1.0 else 'Negative'}")
        print(f"Majority class baseline accuracy: {baseline_accuracy:.4f} ({baseline_accuracy*100:.2f}%)")
        print(f"Majority class baseline error rate: {baseline_error_rate:.4f} ({baseline_error_rate*100:.2f}%)")
        print(f"Class imbalance ratio: {results['class_imbalance_ratio']:.2f}:1")

        return results

    

    def generate_comprehensive(self):
        """
        Generate comprehensive analysis report
        """


        # 1. Label prevalence analysis
        prevalence = self.analyze_label_prevalence()
        self.plot_label_prevalence(prevalence)

        # 2. Co-occurrence analysis
        cooccurrence = self.analyze_cooccurrence()
        self.plot_cooccurrence_matrix(cooccurrence)

        # 3. Demographic distribution analysis
        demographics = self.analyze_distribution_by_demographics()
        self.plot_demographic_distributions(demographics)

        # 4. Class priors and baseline analysis
        baseline = self.calculate_class_priors_and_baseline()





        return {
            'prevalence': prevalence,
            'cooccurrence': cooccurrence,
            'demographics': demographics,
            'baseline': baseline
        }

analyzer = CheXpertAnalyzer(train_df=train_df, val_df=val_df, target_pathology="Pleural Effusion")