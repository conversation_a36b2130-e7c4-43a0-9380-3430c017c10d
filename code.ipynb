{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading CheXpert dataset using datasets library...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Downloading data:   4%|▍         | 1/23 [00:00<00:16,  1.33files/s]"]}], "source": ["from sklearn.model_selection import train_test_split\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from collections import Counter\n", "import re\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from collections import Counter\n", "import os\n", "import warnings\n", "from datasets import load_dataset\n", "warnings.filterwarnings(\"ignore\", category=UserWarning, module=\"tqdm\")\n", "\n", "def extract_patient_id(path):\n", "    \"\"\"Extract patient ID from path\"\"\"\n", "    # Use regex to extract patient ID, e.g., extract \"patient00001\" from \"CheXpert-v1.0-small/train/patient00001/study1/view1_frontal.jpg\"\n", "    match = re.search(r'patient(\\d+)', path)\n", "    if match:\n", "        return f\"patient{match.group(1)}\"\n", "    return None\n", "\n", "def load_chexpert_data_from_datasets():\n", "    \"\"\"\n", "    Load CheXpert dataset using datasets library\n", "    \n", "    Returns:\n", "        combined_df: Combined DataFrame\n", "    \"\"\"\n", "    print(\"Loading CheXpert dataset using datasets library...\")\n", "    \n", "    # Load dataset using datasets library\n", "    ds = load_dataset(\"danjacobellis/chexpert\")\n", "    \n", "    # Convert to pandas DataFrames\n", "    train_df = ds['train'].to_pandas()\n", "    valid_df = ds['valid'].to_pandas()\n", "    \n", "    # Add data source identifier\n", "    train_df['original_split'] = 'train'\n", "    valid_df['original_split'] = 'valid'\n", "    \n", "    # Combine data\n", "    combined_df = pd.concat([train_df, valid_df], ignore_index=True)\n", "    \n", "    print(f\"Total loaded records: {len(combined_df)}\")\n", "    print(f\"Original training records: {len(train_df)}\")\n", "    print(f\"Original validation records: {len(valid_df)}\")\n", "    \n", "    return combined_df\n", "\n", "\n", "df = load_chexpert_data_from_datasets()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Filtering frontal views...\n", "Original records: 223648\n", "Frontal view records: 191229\n", "Filtered lateral views: 32419\n"]}], "source": ["def filter_frontal_views(df):\n", "    \"\"\"\n", "    Keep only frontal view images\n", "    \n", "    Args:\n", "        df: Original DataFrame\n", "    \n", "    Returns:\n", "        filtered_df: Filtered DataFrame\n", "    \"\"\"\n", "    print(\"\\nFiltering frontal views...\")\n", "    \n", "    # Filter frontal views\n", "    frontal_df = df[df['Frontal/Lateral'] == 'Frontal'].copy()\n", "    \n", "    print(f\"Original records: {len(df)}\")\n", "    print(f\"Frontal view records: {len(frontal_df)}\")\n", "    print(f\"Filtered lateral views: {len(df) - len(frontal_df)}\")\n", "    \n", "    return frontal_df\n", "frontal_df = filter_frontal_views(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Creating patient-level 80/20 split...\n", "Number of unique patients: 64734\n", "Training patients: 51787\n", "Validation patients: 12947\n", "Training images: 152827\n", "Validation images: 38402\n", "Verification passed: No patient overlap between training and validation sets\n"]}], "source": ["def create_patient_level_split(df, test_size=0.2, random_state=42):\n", "    \"\"\"\n", "    Create patient-level train/validation split\n", "    \n", "    Args:\n", "        df: DataFrame containing image data\n", "        test_size: Validation set proportion\n", "        random_state: Random seed\n", "    \n", "    Returns:\n", "        train_df, val_df: Training and validation DataFrames\n", "    \"\"\"\n", "    print(f\"\\nCreating patient-level {int((1-test_size)*100)}/{int(test_size*100)} split...\")\n", "    \n", "    # Extract patient IDs\n", "    df['patient_id'] = df['Path'].apply(extract_patient_id)\n", "    \n", "    # Get unique patient IDs\n", "    unique_patients = df['patient_id'].unique()\n", "    print(f\"Number of unique patients: {len(unique_patients)}\")\n", "    \n", "    # Split by patient ID\n", "    train_patients, val_patients = train_test_split(\n", "        unique_patients, \n", "        test_size=test_size, \n", "        random_state=random_state\n", "    )\n", "    \n", "    print(f\"Training patients: {len(train_patients)}\")\n", "    print(f\"Validation patients: {len(val_patients)}\")\n", "    \n", "    # Split data based on patient IDs\n", "    train_df = df[df['patient_id'].isin(train_patients)].copy()\n", "    val_df = df[df['patient_id'].isin(val_patients)].copy()\n", "    \n", "    print(f\"Training images: {len(train_df)}\")\n", "    print(f\"Validation images: {len(val_df)}\")\n", "    \n", "    # Verify no patient overlap\n", "    train_patient_set = set(train_df['patient_id'].unique())\n", "    val_patient_set = set(val_df['patient_id'].unique())\n", "    overlap = train_patient_set.intersection(val_patient_set)\n", "    \n", "    if len(overlap) > 0:\n", "        print(f\"Warning: Found overlapping patients: {overlap}\")\n", "    else:\n", "        print(\"Verification passed: No patient overlap between training and validation sets\")\n", "    \n", "    return train_df, val_df\n", "train_df, val_df = create_patient_level_split(frontal_df, test_size=0.2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==================================================\n", "Dataset Statistical Analysis\n", "==================================================\n", "Training set: 152827 images, 51787 patients\n", "Validation set: 38402 images, 12947 patients\n", "\n", "Gender distribution:\n", "Training set:\n", "Sex\n", "Male       89753\n", "Female     63073\n", "Unknown        1\n", "Name: count, dtype: int64\n", "Validation set:\n", "Sex\n", "Male      22506\n", "Female    15896\n", "Name: count, dtype: int64\n", "\n", "Age distribution:\n", "Training set age: mean 60.7 ± 17.8\n", "Validation set age: mean 60.5 ± 17.7\n", "\n", "AP/PA distribution:\n", "Training set:\n", "AP/PA\n", "AP    129254\n", "PA     23559\n", "LL        13\n", "RL         1\n", "Name: count, dtype: int64\n", "Validation set:\n", "AP/PA\n", "AP    32505\n", "PA     5894\n", "LL        3\n", "Name: count, dtype: int64\n"]}], "source": ["def analyze_dataset_statistics(train_df, val_df):\n", "    \"\"\"\n", "    Analyze dataset statistics\n", "    \n", "    Args:\n", "        train_df: Training DataFrame\n", "        val_df: Validation DataFrame\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*50)\n", "    print(\"Dataset Statistical Analysis\")\n", "    print(\"=\"*50)\n", "    \n", "    # Basic statistics\n", "    print(f\"Training set: {len(train_df)} images, {len(train_df['patient_id'].unique())} patients\")\n", "    print(f\"Validation set: {len(val_df)} images, {len(val_df['patient_id'].unique())} patients\")\n", "    \n", "    # Gender distribution\n", "    print(\"\\nGender distribution:\")\n", "    print(\"Training set:\")\n", "    print(train_df['Sex'].value_counts())\n", "    print(\"Validation set:\")\n", "    print(val_df['Sex'].value_counts())\n", "    \n", "    # Age distribution\n", "    print(f\"\\nAge distribution:\")\n", "    print(f\"Training set age: mean {train_df['Age'].mean():.1f} ± {train_df['Age'].std():.1f}\")\n", "    print(f\"Validation set age: mean {val_df['Age'].mean():.1f} ± {val_df['Age'].std():.1f}\")\n", "    \n", "    # AP/PA distribution\n", "    print(\"\\nAP/PA distribution:\")\n", "    print(\"Training set:\")\n", "    print(train_df['AP/PA'].value_counts())\n", "    print(\"Validation set:\")\n", "    print(val_df['AP/PA'].value_counts())\n", "analyze_dataset_statistics(train_df, val_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Saving split results to chexpert_splits/...\n", "  - train_frontal.csv: 152827 records\n", "  - val_frontal.csv: 38402 records\n", "  - train_patients.txt: 51787 patients\n", "  - val_patients.txt: 12947 patients\n"]}], "source": ["def save_splits(train_df, val_df, output_dir=\"chexpert_splits\"):\n", "    \"\"\"\n", "    Save split datasets\n", "    \n", "    Args:\n", "        train_df: Training DataFrame\n", "        val_df: Validation DataFrame\n", "        output_dir: Output directory\n", "    \"\"\"\n", "    print(f\"\\nSaving split results to {output_dir}/...\")\n", "    \n", "    # Create output directory\n", "    os.makedirs(output_dir, exist_ok=True)\n", "    \n", "    # Save CSV files\n", "    train_df.to_csv(os.path.join(output_dir, \"train_frontal.csv\"), index=False)\n", "    val_df.to_csv(os.path.join(output_dir, \"val_frontal.csv\"), index=False)\n", "    \n", "    # Save patient ID lists\n", "    train_patients = sorted(train_df['patient_id'].unique())\n", "    val_patients = sorted(val_df['patient_id'].unique())\n", "    \n", "    with open(os.path.join(output_dir, \"train_patients.txt\"), 'w') as f:\n", "        f.write('\\n'.join(train_patients))\n", "    \n", "    with open(os.path.join(output_dir, \"val_patients.txt\"), 'w') as f:\n", "        f.write('\\n'.join(val_patients))\n", "    \n", "    print(f\"  - train_frontal.csv: {len(train_df)} records\")\n", "    print(f\"  - val_frontal.csv: {len(val_df)} records\")\n", "    print(f\"  - train_patients.txt: {len(train_patients)} patients\")\n", "    print(f\"  - val_patients.txt: {len(val_patients)} patients\")\n", "save_splits(train_df, val_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class CheXpertAnalyzer:\n", "    \"\"\"CheXpert Dataset Analyzer\"\"\"\n", "    \n", "    def __init__(self, train_df=None, val_df=None, target_pathology=\"Pleural Effusion\"):\n", "        \"\"\"\n", "        Initialize the analyzer\n", "        \n", "        Args:\n", "            train_df: Training DataFrame (if None, will load from datasets)\n", "            val_df: Validation DataFrame (if None, will load from datasets)\n", "            target_pathology: Target pathology label\n", "        \"\"\"\n", "        self.target_pathology = target_pathology\n", "        \n", "        if train_df is not None and val_df is not None:\n", "            # Use provided DataFrames\n", "            self.train_df = train_df\n", "            self.val_df = val_df\n", "            self.combined_df = pd.concat([self.train_df, self.val_df], ignore_index=True)\n", "            print(\"Using provided train/val DataFrames\")\n", "        else:\n", "            # Fallback to loading data\n", "            print(\"Loading CheXpert dataset using datasets library...\")\n", "            ds = load_dataset(\"danjacobellis/chexpert\")\n", "            self.train_df = ds['train'].to_pandas()\n", "            self.val_df = ds['valid'].to_pandas()\n", "            self.combined_df = pd.concat([self.train_df, self.val_df], ignore_index=True)\n", "        \n", "        # Define all pathology labels\n", "        self.pathology_labels = [\n", "            'No Finding', 'Enlarged Cardiomediastinum', 'Cardiomegaly',\n", "            'Lung Opacity', 'Lung Lesion', 'Edema', 'Consolidation',\n", "            'Pneumonia', 'Atelectasis', 'Pneumothorax', 'Pleural Effusion',\n", "            'Pleural Other', 'Fracture', 'Support Devices'\n", "        ]\n", "        \n", "        print(f\"Target pathology for analysis: {self.target_pathology}\")\n", "        print(f\"Training samples: {len(self.train_df)}\")\n", "        print(f\"Validation samples: {len(self.val_df)}\")\n", "        print(f\"Total samples: {len(self.combined_df)}\")\n", "\n", "    \n", "    def analyze_label_prevalence(self):\n", "        \"\"\"\n", "        Analyze label prevalence (positive/negative/uncertain/unlabeled)\n", "        \n", "        Returns:\n", "            dict: Dictionary containing counts and proportions for each category\n", "        \"\"\"\n", "        print(f\"\\n=== {self.target_pathology} Label Prevalence Analysis ===\")\n", "        \n", "        target_column = self.combined_df[self.target_pathology]\n", "        \n", "        # Calculate counts for each category\n", "        positive = (target_column == 1.0).sum()\n", "        negative = (target_column == 0.0).sum()\n", "        uncertain = (target_column == -1.0).sum()\n", "        unlabeled = target_column.isna().sum()\n", "        total = len(target_column)\n", "        \n", "        prevalence = {\n", "            'positive': positive,\n", "            'negative': negative,\n", "            'uncertain': uncertain,\n", "            'unlabeled': unlabeled,\n", "            'total': total,\n", "            'positive_rate': positive / total * 100,\n", "            'negative_rate': negative / total * 100,\n", "            'uncertain_rate': uncertain / total * 100,\n", "            'unlabeled_rate': unlabeled / total * 100\n", "        }\n", "        \n", "        print(f\"Positive: {positive:,} ({prevalence['positive_rate']:.2f}%)\")\n", "        print(f\"Negative: {negative:,} ({prevalence['negative_rate']:.2f}%)\")\n", "        print(f\"Uncertain: {uncertain:,} ({prevalence['uncertain_rate']:.2f}%)\")\n", "        print(f\"Unlabeled: {unlabeled:,} ({prevalence['unlabeled_rate']:.2f}%)\")\n", "        \n", "        return prevalence\n", "    \n", "    def plot_label_prevalence(self, prevalence):\n", "        \"\"\"\n", "        Plot label prevalence charts\n", "        \n", "        Args:\n", "            prevalence: Label prevalence dictionary\n", "        \"\"\"\n", "        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "        \n", "        # Pie chart\n", "        labels = ['Positive', 'Negative', 'Uncertain', 'Unlabeled']\n", "        sizes = [prevalence['positive'], prevalence['negative'], \n", "                prevalence['uncertain'], prevalence['unlabeled']]\n", "        colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99']\n", "        \n", "        # Only show non-zero categories\n", "        non_zero_indices = [i for i, size in enumerate(sizes) if size > 0]\n", "        labels_filtered = [labels[i] for i in non_zero_indices]\n", "        sizes_filtered = [sizes[i] for i in non_zero_indices]\n", "        colors_filtered = [colors[i] for i in non_zero_indices]\n", "        \n", "        ax1.pie(sizes_filtered, labels=labels_filtered, colors=colors_filtered, \n", "                autopct='%1.1f%%', startangle=90)\n", "        ax1.set_title(f'{self.target_pathology} Label Distribution')\n", "        \n", "        # Bar chart\n", "        ax2.bar(labels_filtered, sizes_filtered, color=colors_filtered)\n", "        ax2.set_title(f'{self.target_pathology} Label Counts')\n", "        ax2.set_ylabel('Number of Samples')\n", "        ax2.tick_params(axis='x', rotation=45)\n", "        \n", "        # Add value labels\n", "        for i, v in enumerate(sizes_filtered):\n", "            ax2.text(i, v + max(sizes_filtered) * 0.01, f'{v:,}', \n", "                    ha='center', va='bottom')\n", "        \n", "        plt.tight_layout()\n", "        plt.savefig(f'{self.target_pathology.replace(\" \", \"_\")}_prevalence.png', \n", "                   dpi=300, bbox_inches='tight')\n", "        plt.show()\n", "    \n", "    def analyze_cooccurrence(self, second_pathology=\"Cardiomegaly\"):\n", "        \"\"\"\n", "        Analyze co-occurrence with \"No Finding\" and a second pathology\n", "        \n", "        Args:\n", "            second_pathology: Second pathology label\n", "        \n", "        Returns:\n", "            dict: Co-occurrence analysis results\n", "        \"\"\"\n", "        print(f\"\\n=== {self.target_pathology} Co-occurrence Analysis ===\")\n", "        \n", "        # Co-occurrence with \"No Finding\"\n", "        no_finding_cooccur = self._analyze_pairwise_cooccurrence(\n", "            self.target_pathology, \"No Finding\"\n", "        )\n", "        \n", "        # Co-occurrence with second pathology\n", "        second_cooccur = self._analyze_pairwise_cooccurrence(\n", "            self.target_pathology, second_pathology\n", "        )\n", "        \n", "        return {\n", "            'no_finding': no_finding_cooccur,\n", "            'second_pathology': second_cooccur,\n", "            'second_pathology_name': second_pathology\n", "        }\n", "    \n", "    def _analyze_pairwise_cooccurrence(self, pathology1, pathology2):\n", "        \"\"\"\n", "        Analyze pairwise co-occurrence between two pathology labels\n", "        \n", "        Args:\n", "            pathology1: First pathology label\n", "            pathology2: Second pathology label\n", "        \n", "        Returns:\n", "            dict: Co-occurrence statistics\n", "        \"\"\"\n", "        # Only consider samples with definitive labels (1 or 0) for both pathologies\n", "        mask = (self.combined_df[pathology1].isin([0.0, 1.0])) & \\\n", "               (self.combined_df[pathology2].isin([0.0, 1.0]))\n", "        \n", "        subset = self.combined_df[mask]\n", "        \n", "        if len(subset) == 0:\n", "            return {'error': 'No samples with both pathologies labeled'}\n", "        \n", "        # Calculate co-occurrence matrix\n", "        both_positive = ((subset[pathology1] == 1.0) & (subset[pathology2] == 1.0)).sum()\n", "        path1_pos_path2_neg = ((subset[pathology1] == 1.0) & (subset[pathology2] == 0.0)).sum()\n", "        path1_neg_path2_pos = ((subset[pathology1] == 0.0) & (subset[pathology2] == 1.0)).sum()\n", "        both_negative = ((subset[pathology1] == 0.0) & (subset[pathology2] == 0.0)).sum()\n", "        \n", "        total_valid = len(subset)\n", "        \n", "        result = {\n", "            'pathology1': pathology1,\n", "            'pathology2': pathology2,\n", "            'both_positive': both_positive,\n", "            'path1_pos_path2_neg': path1_pos_path2_neg,\n", "            'path1_neg_path2_pos': path1_neg_path2_pos,\n", "            'both_negative': both_negative,\n", "            'total_valid': total_valid,\n", "            'cooccurrence_rate': both_positive / total_valid * 100 if total_valid > 0 else 0\n", "        }\n", "        \n", "        print(f\"\\n{pathology1} vs {pathology2} Co-occurrence Analysis:\")\n", "        print(f\"Valid samples: {total_valid:,}\")\n", "        print(f\"Both positive: {both_positive:,} ({both_positive/total_valid*100:.2f}%)\")\n", "        print(f\"{pathology1} positive, {pathology2} negative: {path1_pos_path2_neg:,}\")\n", "        print(f\"{pathology1} negative, {pathology2} positive: {path1_neg_path2_pos:,}\")\n", "        print(f\"Both negative: {both_negative:,} ({both_negative/total_valid*100:.2f}%)\")\n", "        \n", "        return result\n", "    \n", "    def plot_cooccurrence_matrix(self, cooccurrence_data):\n", "        \"\"\"\n", "        Plot co-occurrence matrix heatmaps\n", "        \n", "        Args:\n", "            cooccurrence_data: Co-occurrence analysis data\n", "        \"\"\"\n", "        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "        \n", "        # No Finding co-occurrence matrix\n", "        no_finding_data = cooccurrence_data['no_finding']\n", "        if 'error' not in no_finding_data:\n", "            matrix1 = np.array([\n", "                [no_finding_data['both_negative'], no_finding_data['path1_neg_path2_pos']],\n", "                [no_finding_data['path1_pos_path2_neg'], no_finding_data['both_positive']]\n", "            ])\n", "            \n", "            sns.heatmap(matrix1, annot=True, fmt='d', cmap='Blues', ax=ax1,\n", "                       xticklabels=['No Finding-', 'No Finding+'],\n", "                       yticklabels=[f'{self.target_pathology}-', f'{self.target_pathology}+'])\n", "            ax1.set_title(f'{self.target_pathology} vs No Finding')\n", "        \n", "        # Second pathology co-occurrence matrix\n", "        second_data = cooccurrence_data['second_pathology']\n", "        if 'error' not in second_data:\n", "            matrix2 = np.array([\n", "                [second_data['both_negative'], second_data['path1_neg_path2_pos']],\n", "                [second_data['path1_pos_path2_neg'], second_data['both_positive']]\n", "            ])\n", "            \n", "            second_name = cooccurrence_data['second_pathology_name']\n", "            sns.heatmap(matrix2, annot=True, fmt='d', cmap='Reds', ax=ax2,\n", "                       xticklabels=[f'{second_name}-', f'{second_name}+'],\n", "                       yticklabels=[f'{self.target_pathology}-', f'{self.target_pathology}+'])\n", "            ax2.set_title(f'{self.target_pathology} vs {second_name}')\n", "        \n", "        plt.tight_layout()\n", "        plt.savefig(f'{self.target_pathology.replace(\" \", \"_\")}_cooccurrence.png', \n", "                   dpi=300, bbox_inches='tight')\n", "        plt.show()\n", "    \n", "    def analyze_distribution_by_demographics(self):\n", "        \"\"\"\n", "        Analyze distribution by view type (AP vs PA) and sex/age\n", "        \n", "        Returns:\n", "            dict: Demographic distribution analysis results\n", "        \"\"\"\n", "        print(f\"\\n=== {self.target_pathology} Demographic Distribution Analysis ===\")\n", "        \n", "        # Handle AP/PA mapping (assuming 0=AP, 1=PA based on HF page warning)\n", "        df_copy = self.combined_df.copy()\n", "        \n", "        # Create numeric mapping for AP/PA\n", "        ap_pa_mapping = {'AP': 0, 'PA': 1}\n", "        df_copy['AP_PA_numeric'] = df_copy['AP/PA'].map(ap_pa_mapping)\n", "        \n", "        print(\"Note: Based on HuggingFace page warning, assuming AP/PA mapping: 0=AP, 1=PA\")\n", "        \n", "        # Only analyze samples with definitive labels\n", "        valid_samples = df_copy[df_copy[self.target_pathology].isin([0.0, 1.0])]\n", "        positive_samples = valid_samples[valid_samples[self.target_pathology] == 1.0]\n", "        \n", "        results = {}\n", "        \n", "        # Distribution by AP/PA\n", "        print(f\"\\nDistribution by View Type:\")\n", "        ap_pa_dist = self._analyze_categorical_distribution(\n", "            valid_samples, positive_samples, 'AP/PA'\n", "        )\n", "        results['ap_pa'] = ap_pa_dist\n", "        \n", "        # Distribution by sex\n", "        print(f\"\\nDistribution by Sex:\")\n", "        sex_dist = self._analyze_categorical_distribution(\n", "            valid_samples, positive_samples, 'Sex'\n", "        )\n", "        results['sex'] = sex_dist\n", "        \n", "        # Distribution by age\n", "        print(f\"\\nDistribution by Age:\")\n", "        age_dist = self._analyze_age_distribution(valid_samples, positive_samples)\n", "        results['age'] = age_dist\n", "        \n", "        return results\n", "    \n", "    def _analyze_categorical_distribution(self, valid_samples, positive_samples, column):\n", "        \"\"\"\n", "        Analyze distribution of categorical variables\n", "        \n", "        Args:\n", "            valid_samples: Valid samples\n", "            positive_samples: Positive samples\n", "            column: Column name to analyze\n", "        \n", "        Returns:\n", "            dict: Distribution statistics\n", "        \"\"\"\n", "        total_counts = valid_samples[column].value_counts()\n", "        positive_counts = positive_samples[column].value_counts()\n", "        \n", "        result = {}\n", "        for category in total_counts.index:\n", "            total = total_counts[category]\n", "            positive = positive_counts.get(category, 0)\n", "            prevalence = positive / total * 100 if total > 0 else 0\n", "            \n", "            result[category] = {\n", "                'total': total,\n", "                'positive': positive,\n", "                'prevalence': prevalence\n", "            }\n", "            \n", "            print(f\"{category}: {positive}/{total} ({prevalence:.2f}%)\")\n", "        \n", "        return result\n", "    \n", "    def _analyze_age_distribution(self, valid_samples, positive_samples):\n", "        \"\"\"\n", "        Analyze age distribution\n", "        \n", "        Args:\n", "            valid_samples: Valid samples\n", "            positive_samples: Positive samples\n", "        \n", "        Returns:\n", "            dict: Age distribution statistics\n", "        \"\"\"\n", "        # Create age groups\n", "        age_bins = [0, 30, 50, 70, 100]\n", "        age_labels = ['<30', '30-50', '50-70', '70+']\n", "        \n", "        valid_samples_copy = valid_samples.copy()\n", "        positive_samples_copy = positive_samples.copy()\n", "        \n", "        valid_samples_copy['age_group'] = pd.cut(\n", "            valid_samples_copy['Age'], bins=age_bins, labels=age_labels, right=False\n", "        )\n", "        positive_samples_copy['age_group'] = pd.cut(\n", "            positive_samples_copy['Age'], bins=age_bins, labels=age_labels, right=False\n", "        )\n", "        \n", "        age_dist = self._analyze_categorical_distribution(\n", "            valid_samples_copy, positive_samples_copy, 'age_group'\n", "        )\n", "        \n", "        # Add continuous age statistics\n", "        age_stats = {\n", "            'all_mean': valid_samples['Age'].mean(),\n", "            'all_std': valid_samples['Age'].std(),\n", "            'positive_mean': positive_samples['Age'].mean(),\n", "            'positive_std': positive_samples['Age'].std()\n", "        }\n", "        \n", "        print(f\"All samples age: {age_stats['all_mean']:.1f} ± {age_stats['all_std']:.1f}\")\n", "        print(f\"Positive samples age: {age_stats['positive_mean']:.1f} ± {age_stats['positive_std']:.1f}\")\n", "        \n", "        return {'groups': age_dist, 'stats': age_stats}\n", "\n", "    def plot_demographic_distributions(self, demo_results):\n", "        \"\"\"\n", "        Plot demographic distribution charts\n", "\n", "        Args:\n", "            demo_results: Demographic distribution analysis results\n", "        \"\"\"\n", "        fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "        # AP/PA distribution\n", "        ap_pa_data = demo_results['ap_pa']\n", "        categories = list(ap_pa_data.keys())\n", "        prevalences = [ap_pa_data[cat]['prevalence'] for cat in categories]\n", "        totals = [ap_pa_data[cat]['total'] for cat in categories]\n", "\n", "        ax1 = axes[0, 0]\n", "        bars1 = ax1.bar(categories, prevalences, color=['skyblue', 'lightcoral'])\n", "        ax1.set_title(f'{self.target_pathology} Prevalence by View Type')\n", "        ax1.set_ylabel('Prevalence (%)')\n", "        ax1.set_xlabel('View Type (Assumed: 0=AP, 1=PA)')\n", "\n", "        # Add sample count labels\n", "        for i, (bar, total) in enumerate(zip(bars1, totals)):\n", "            height = bar.get_height()\n", "            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "                    f'n={total:,}', ha='center', va='bottom', fontsize=10)\n", "\n", "        # Sex distribution\n", "        sex_data = demo_results['sex']\n", "        sex_categories = list(sex_data.keys())\n", "        sex_prevalences = [sex_data[cat]['prevalence'] for cat in sex_categories]\n", "        sex_totals = [sex_data[cat]['total'] for cat in sex_categories]\n", "\n", "        ax2 = axes[0, 1]\n", "        bars2 = ax2.bar(sex_categories, sex_prevalences, color=['lightgreen', 'orange'])\n", "        ax2.set_title(f'{self.target_pathology} Prevalence by Sex')\n", "        ax2.set_ylabel('Prevalence (%)')\n", "        ax2.set_xlabel('Sex')\n", "\n", "        for i, (bar, total) in enumerate(zip(bars2, sex_totals)):\n", "            height = bar.get_height()\n", "            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "                    f'n={total:,}', ha='center', va='bottom', fontsize=10)\n", "\n", "        # Age group distribution\n", "        age_data = demo_results['age']['groups']\n", "        age_categories = list(age_data.keys())\n", "        age_prevalences = [age_data[cat]['prevalence'] for cat in age_categories]\n", "        age_totals = [age_data[cat]['total'] for cat in age_categories]\n", "\n", "        ax3 = axes[1, 0]\n", "        bars3 = ax3.bar(age_categories, age_prevalences, color='mediumpurple')\n", "        ax3.set_title(f'{self.target_pathology} Prevalence by Age Group')\n", "        ax3.set_ylabel('Prevalence (%)')\n", "        ax3.set_xlabel('Age Group')\n", "\n", "        for i, (bar, total) in enumerate(zip(bars3, age_totals)):\n", "            height = bar.get_height()\n", "            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "                    f'n={total:,}', ha='center', va='bottom', fontsize=10)\n", "\n", "        # Age distribution histogram\n", "        ax4 = axes[1, 1]\n", "        valid_samples = self.combined_df[self.combined_df[self.target_pathology].isin([0.0, 1.0])]\n", "        positive_samples = valid_samples[valid_samples[self.target_pathology] == 1.0]\n", "        negative_samples = valid_samples[valid_samples[self.target_pathology] == 0.0]\n", "\n", "        ax4.hist(negative_samples['Age'], bins=20, alpha=0.7, label='Negative', color='lightblue')\n", "        ax4.hist(positive_samples['Age'], bins=20, alpha=0.7, label='Positive', color='lightcoral')\n", "        ax4.set_title(f'{self.target_pathology} Age Distribution')\n", "        ax4.set_xlabel('Age')\n", "        ax4.set_ylabel('Number of Samples')\n", "        ax4.legend()\n", "\n", "        plt.tight_layout()\n", "        plt.savefig(f'{self.target_pathology.replace(\" \", \"_\")}_demographics.png',\n", "                   dpi=300, bbox_inches='tight')\n", "        plt.show()\n", "\n", "    def calculate_class_priors_and_baseline(self):\n", "        \"\"\"\n", "        Calculate class prior probabilities and majority class baseline error rate\n", "\n", "        Returns:\n", "            dict: Dictionary containing prior probabilities and baseline error rate\n", "        \"\"\"\n", "        print(f\"\\n=== {self.target_pathology} Class Priors and Baseline Analysis ===\")\n", "\n", "        # Only consider samples with definitive labels (0 or 1)\n", "        valid_samples = self.combined_df[self.combined_df[self.target_pathology].isin([0.0, 1.0])]\n", "\n", "        if len(valid_samples) == 0:\n", "            print(\"Error: No valid labeled samples\")\n", "            return None\n", "\n", "        positive_count = (valid_samples[self.target_pathology] == 1.0).sum()\n", "        negative_count = (valid_samples[self.target_pathology] == 0.0).sum()\n", "        total_valid = len(valid_samples)\n", "\n", "        # Calculate prior probabilities\n", "        positive_prior = positive_count / total_valid\n", "        negative_prior = negative_count / total_valid\n", "\n", "        # Calculate majority class baseline error rate (0-1 loss)\n", "        majority_class = 1.0 if positive_count > negative_count else 0.0\n", "        minority_count = min(positive_count, negative_count)\n", "        baseline_error_rate = minority_count / total_valid\n", "        baseline_accuracy = 1 - baseline_error_rate\n", "\n", "        results = {\n", "            'total_valid_samples': total_valid,\n", "            'positive_count': positive_count,\n", "            'negative_count': negative_count,\n", "            'positive_prior': positive_prior,\n", "            'negative_prior': negative_prior,\n", "            'majority_class': majority_class,\n", "            'baseline_accuracy': baseline_accuracy,\n", "            'baseline_error_rate': baseline_error_rate,\n", "            'class_imbalance_ratio': max(positive_count, negative_count) / min(positive_count, negative_count)\n", "        }\n", "\n", "        print(f\"Total valid samples: {total_valid:,}\")\n", "        print(f\"Positive samples: {positive_count:,} (prior: {positive_prior:.4f})\")\n", "        print(f\"Negative samples: {negative_count:,} (prior: {negative_prior:.4f})\")\n", "        print(f\"Majority class: {'Positive' if majority_class == 1.0 else 'Negative'}\")\n", "        print(f\"Majority class baseline accuracy: {baseline_accuracy:.4f} ({baseline_accuracy*100:.2f}%)\")\n", "        print(f\"Majority class baseline error rate: {baseline_error_rate:.4f} ({baseline_error_rate*100:.2f}%)\")\n", "        print(f\"Class imbalance ratio: {results['class_imbalance_ratio']:.2f}:1\")\n", "\n", "        return results\n", "\n", "    \n", "\n", "    def generate_comprehensive(self):\n", "        \"\"\"\n", "        Generate comprehensive analysis report\n", "        \"\"\"\n", "\n", "\n", "        # 1. Label prevalence analysis\n", "        prevalence = self.analyze_label_prevalence()\n", "        self.plot_label_prevalence(prevalence)\n", "\n", "        # 2. Co-occurrence analysis\n", "        cooccurrence = self.analyze_cooccurrence()\n", "        self.plot_cooccurrence_matrix(cooccurrence)\n", "\n", "        # 3. Demographic distribution analysis\n", "        demographics = self.analyze_distribution_by_demographics()\n", "        self.plot_demographic_distributions(demographics)\n", "\n", "        # 4. Class priors and baseline analysis\n", "        baseline = self.calculate_class_priors_and_baseline()\n", "\n", "\n", "\n", "\n", "\n", "        return {\n", "            'prevalence': prevalence,\n", "            'cooccurrence': cooccurrence,\n", "            'demographics': demographics,\n", "            'baseline': baseline\n", "        }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Target pathology for analysis: Pleural Effusion\n", "Training samples: 152827\n", "Validation samples: 38402\n", "Total samples: 191229\n", "\n", "=== Pleural Effusion Label Prevalence Analysis ===\n", "Positive: 76,963 (40.25%)\n", "Negative: 25,437 (13.30%)\n", "Uncertain: 9,578 (5.01%)\n", "Unlabeled: 79,251 (41.44%)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== Pleural Effusion Co-occurrence Analysis ===\n", "\n", "Pleural Effusion vs No Finding Co-occurrence Analysis:\n", "Valid samples: 8,093\n", "Both positive: 0 (0.00%)\n", "Pleural Effusion positive, No Finding negative: 64\n", "Pleural Effusion negative, No Finding positive: 7,917\n", "Both negative: 112 (1.38%)\n", "\n", "Pleural Effusion vs Cardiomegaly Co-occurrence Analysis:\n", "Valid samples: 18,584\n", "Both positive: 10,530 (56.66%)\n", "Pleural Effusion positive, Cardiomegaly negative: 1,283\n", "Pleural Effusion negative, Cardiomegaly positive: 2,832\n", "Both negative: 3,939 (21.20%)\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABbcAAAJOCAYAAACEOzolAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjQuMywgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/MnkTPAAAACXBIWXMAAA9hAAAPYQGoP6dpAACf3UlEQVR4nOzdeXxM9/7H8fdMdtIsliRijX2ntCVV1FIpadHSXi0araUIraWoXrV1SVG7okrRiyotamnt21WxFlVKtVVaJLFFaksiOb8//DLXNKEJM5OMeT3v4zyu+Z7vnO/nnIz6zDef8z0mwzAMAQAAAAAAAADgRMy5HQAAAAAAAAAAADnF5DYAAAAAAAAAwOkwuQ0AAAAAAAAAcDpMbgMAAAAAAAAAnA6T2wAAAAAAAAAAp8PkNgAAAAAAAADA6TC5DQAAAAAAAABwOkxuAwAAAAAAAACcDpPbAAAAAAAAAACnw+Q2cBc2b94sk8mkzZs353Yo/6hUqVLq1KmTTY/5n//8RxUrVpSHh4cCAgIs7WPGjFHp0qXl5uammjVr2nRMSerUqZNKlSpl8+PC2u+//y6TyaQ5c+bY9ZjDhw+XyWSy2RgAAMA5kEuTS9+PyHf/h88aAEdichu4xZw5c2QymSybt7e3ypcvr169eik+Pj63w7OrW8/771v37t0t/Y4cOaJOnTqpTJky+uSTTzRjxgxJ0tq1azVw4EDVq1dPs2fP1vvvv59bp+J0Mr7gmUwm7d27N9P+Tp06ydfX12bj/f1zfuv25ptv2mwcAADgWsilyaVzU3x8vN544w1VrFhR+fLlU/78+VW7dm29++67SkxMzO3wAAB24p7bAQB50ciRIxUWFqbr169r27ZtmjZtmr755hv9+OOPypcvX26HZzdPPPGEXnrppUzt5cuXt/x58+bNSk9P18SJE1W2bFlL+8aNG2U2mzVr1ix5enraJb5PPvlE6enpdjl2XjF8+HCtWLHCIWNlfM5vVbVqVZUsWVLXrl2Th4eHXccfMmQIk+kAANyHyKWtkUvb3+7du9WiRQtdvnxZHTp0UO3atSVJe/bs0QcffKCtW7dq7dq1Do+LfBcA7I/JbSALzZs310MPPSRJ6tKliwoWLKhx48bp66+/1gsvvJBrcaWnpyslJUXe3t52OX758uXVoUOHO/ZJSEiQJKtbKDPafXx87JaMS7L7ZGtuq1mzplauXKnvv/9etWrVsvt4t37O/85en7Fbubu7y92df4YAALjfkEvfHrm07SUmJuqZZ56Rm5ub9u3bp4oVK1rtf++99/TJJ5/YZKyrV6/m6Bc05LsAYH8sSwJkQ+PGjSVJx48fv2O/nTt36sknn5S/v7/y5cunhg0b6rvvvrPqc7v1x7Jaj81kMqlXr16aP3++qlSpIi8vL61evVqS9OGHH+rRRx9VwYIF5ePjo9q1a+vLL7+8h7P8Z6VKldKwYcMkSYULF5bJZLLEPXv2bF25csVy++WcOXPuuHZzxnsz/PXXX+rTp49KlSolLy8vBQUF6YknntD3339v6ZPVtbty5Yr69++v4sWLy8vLSxUqVNCHH34owzAyjderVy8tW7ZMVatWlZeXl6pUqWK5nrcTHx8vd3d3jRgxItO+o0ePymQyacqUKZKk1NRUjRgxQuXKlZO3t7cKFiyoxx57TOvWrbvjGBl69+6twMBAq+tyJ1OnTrV8LkJDQxUdHW2TWy6z+rllLI1y6tQptW7dWr6+vipcuLDeeOMNpaWlWb0/MTFRnTp1kr+/vwICAhQVFZVlXHf6zGfn57R582Y99NBD8vb2VpkyZfTxxx+77LqGAADkZeTSN5FLW7NVLv3xxx/r1KlTGjduXKaJbUkKDg7WkCFDLK+//vprRUZGKjQ0VF5eXipTpozeeeedTDnt448/rqpVq2rv3r1q0KCB8uXLp7feekvSveW7N27c0DvvvKMyZcrIy8tLpUqV0ltvvaXk5GSrfqVKldJTTz1lyXl9fHxUrVo1y1r1S5YsUbVq1eTt7a3atWtr3759mcY/cuSI2rZtqwIFCsjb21sPPfSQli9fnqnfDz/8oIYNG8rHx0fFihXTu+++q9mzZ8tkMun333/P8bW7lWEYKlWqlFq1apVp3/Xr1+Xv769XX331tu8HgH/CrxCBbPj1118lSQULFrxtn40bN6p58+aqXbu2hg0bJrPZrNmzZ6tx48b673//q0ceeeSuxt64caMWLVqkXr16qVChQpaEdOLEiWrZsqXat2+vlJQULVy4UM8995xWrlypyMjIuxrr+vXrOnfuXKZ2Pz8/eXp6asKECfrss8+0dOlSTZs2Tb6+vqpevbrKli2rGTNmaNeuXZo5c6Yk6dFHH83R2N27d9eXX36pXr16qXLlyjp//ry2bdumn3766bZVzIZhqGXLltq0aZM6d+6smjVras2aNRowYIBOnTql8ePHW/Xftm2blixZop49e+qBBx7QpEmT1KZNG508efK2P9vg4GA1bNhQixYtsnwZyfDFF1/Izc1Nzz33nKSbyWtMTIy6dOmiRx55RElJSdqzZ4++//57PfHEE/94Dfz8/NS3b18NHTr0H6u3hw8frhEjRqhp06bq0aOHjh49qmnTpmn37t367rvvslWZc+nSpUw/70KFCt22f1pamiIiIlSnTh19+OGHWr9+vcaOHasyZcqoR48ekm7+TFq1aqVt27ape/fuqlSpkpYuXaqoqKh/jCdDdn5O+/bt05NPPqkiRYpoxIgRSktL08iRI1W4cOFsjwMAAByDXJpc2p659PLly+Xj46O2bdtm61rNmTNHvr6+6tevn3x9fbVx40YNHTpUSUlJGjNmjFXf8+fPq3nz5mrXrp06dOig4ODge853u3Tporlz56pt27bq37+/du7cqZiYGP30009aunSpVd9ffvlFL774ol599VV16NBBH374oZ5++mlNnz5db731lnr27ClJiomJ0fPPP6+jR4/KbL5Zw3jo0CHVq1dPRYsW1Ztvvqn8+fNr0aJFat26tb766is988wzkqRTp06pUaNGMplMGjx4sPLnz6+ZM2fKy8vrnq5dBpPJpA4dOmj06NG6cOGCChQoYNm3YsUKJSUl/eMdDwBwRwYAi9mzZxuSjPXr1xtnz541/vjjD2PhwoVGwYIFDR8fH+PPP/80DMMwNm3aZEgyNm3aZBiGYaSnpxvlypUzIiIijPT0dMvxrl69aoSFhRlPPPGEpS0qKsooWbJkprGHDRtm/P2vpCTDbDYbhw4dytT/6tWrVq9TUlKMqlWrGo0bN7ZqL1mypBEVFfWP5y7pttvnn3+eKc6zZ89avT8qKsrInz+/Vdvx48cNScbs2bOzHG/YsGGW1/7+/kZ0dPQdY/z7tVu2bJkhyXj33Xet+rVt29YwmUzGL7/8YjWep6enVduBAwcMScbkyZPvOO7HH39sSDIOHjxo1V65cmWr612jRg0jMjLyjsfKSsbnafHixUZiYqIRGBhotGzZ0rL/79c2ISHB8PT0NJo1a2akpaVZ2qdMmWJIMj799NM7jpfxOc9qM4ysf25RUVGGJGPkyJFWx3rwwQeN2rVrW15n/ExGjx5tabtx44ZRv379TMe83Wc+Oz+np59+2siXL59x6tQpS9uxY8cMd3f3TMcEAACOQS5NLp0Ve+fSgYGBRo0aNbLd/+8/e8MwjFdffdXIly+fcf36dUtbw4YNDUnG9OnTrfreS767f/9+Q5LRpUsXq2O+8cYbhiRj48aNlraSJUsakozt27db2tasWWNIMnx8fIwTJ05Y2jOuccbfKcMwjCZNmhjVqlWzOqf09HTj0UcfNcqVK2dp6927t2EymYx9+/ZZ2s6fP28UKFDAkGQcP348x9fu75+1o0ePGpKMadOmWb23ZcuWRqlSpaz+3gNATrEsCZCFpk2bqnDhwipevLjatWsnX19fLV26VEWLFs2y//79+3Xs2DG9+OKLOn/+vM6dO6dz587pypUratKkibZu3XrXD29p2LChKleunKndx8fH8ueLFy/q0qVLql+/vtWthznVqlUrrVu3LtPWqFGjuz5mdgUEBGjnzp06ffp0tt/zzTffyM3NTa+99ppVe//+/WUYhr799lur9qZNm6pMmTKW19WrV5efn59+++23O47z7LPPyt3dXV988YWl7ccff9Thw4f1r3/9y+ocDh06pGPHjmX7HP7O399fffr00fLly7O8tVCS1q9fr5SUFPXp08dSmSFJXbt2lZ+fn1atWpWtsT766KNMP+t/0r17d6vX9evXt7p+33zzjdzd3S2V3JLk5uam3r17Zysm6Z9/TmlpaVq/fr1at26t0NBQS7+yZcuqefPm2R4HAADYB7k0ufSt7J1LJyUl6YEHHsh2/1t/9n/99ZfOnTun+vXr6+rVqzpy5IhVXy8vL7388stWbfeS737zzTeSpH79+lm19+/fX5Iy5fGVK1dWeHi45XWdOnUk3Vzqp0SJEpnaM34WFy5c0MaNG/X8889bzvHcuXM6f/68IiIidOzYMZ06dUqStHr1aoWHh6tmzZqW4xUoUEDt27fPFH9Ort2typcvrzp16mj+/PmWtgsXLujbb79V+/btWVYQwD1hWRIgCx999JHKly8vd3d3BQcHq0KFClaTiH+XkYDd6Va0S5cuKTAwMMexhIWFZdm+cuVKvfvuu9q/f7/V+mz3khgUK1ZMTZs2vev334vRo0crKipKxYsXV+3atdWiRQu99NJLKl269G3fc+LECYWGhmZKZitVqmTZf6tbE8AMgYGBunjx4h1jK1SokJo0aaJFixbpnXfekXTzNkp3d3c9++yzln4jR45Uq1atVL58eVWtWlVPPvmkOnbsqOrVq9/55P/m9ddf1/jx4zV8+HB9/fXXmfZnnFeFChWs2j09PVW6dOlM5307jzzyyG0fKJkVb2/vTMt+/P36nThxQkWKFJGvr69Vv7/Heif/9HNKSEjQtWvXVLZs2Uz9smoDAACORS7teK6cS/v5+emvv/66Y59bHTp0SEOGDNHGjRuVlJRkte/SpUtWr4sWLZrpIZ/3ku+eOHFCZrM5U84aEhKigICAf7zm/v7+kqTixYtn2Z7xs/jll19kGIbefvttvf3221nGkpCQoKJFi+rEiRNWE+gZssqrc3Lt/u6ll15Sr169dOLECZUsWVKLFy9WamqqOnbseMf3AcA/YXIbyEJOJ/0yKknGjBlj9RvvW2UkP7dLmG/3EI5bfzue4b///a9atmypBg0aaOrUqSpSpIg8PDw0e/ZsLViwINtx21tOzvX5559X/fr1tXTpUq1du1ZjxozRqFGjtGTJEptV47q5uWXZbvztgTlZadeunV5++WXt379fNWvW1KJFi9SkSROrNaobNGigX3/9VV9//bXWrl2rmTNnavz48Zo+fbq6dOmS7TgzqreHDx9+2+rt3HC76+eocbLzcwIAALmPXNo2yKWzl0tXrFhR+/fvV0pKSqaJ6L9LTExUw4YN5efnp5EjR6pMmTLy9vbW999/r0GDBmW6QyCrz48tZPeXKLe75v/0s8g4jzfeeEMRERFZ9s1pUUhOr93ftWvXTn379tX8+fP11ltvad68eXrooYdyVAQDAFlhchuwgYzb8/z8/P6xWiMwMDDLJ2lnt9pWkr766it5e3trzZo1Vg/6mD17draP4QgZ1TV/P9/bnWuRIkXUs2dP9ezZUwkJCapVq5bee++92ybkJUuW1Pr16/XXX39ZVZxk3BJXsmRJG5zFTa1bt9arr75quZ3y559/1uDBgzP1K1CggF5++WW9/PLLunz5sho0aKDhw4fnaHJbkvr06aMJEyZoxIgRCggIsNqXcV5Hjx61qsZJSUnR8ePHc61iSLoZ24YNG3T58mWrapajR4/abIygoCB5e3vrl19+ybQvqzYAAJC3kUtnjVw6e7n0008/rdjYWH311Vd64YUX7hjH5s2bdf78eS1ZskQNGjSwtB8/fjzb53Iv+W7JkiWVnp6uY8eOWSrkJSk+Pl6JiYk2u+YZ3xE8PDz+8e9UyZIls5VX3+u1K1CggCIjIzV//ny1b99e3333nSZMmJCt9wLAnbDmNmADtWvXVpkyZfThhx/q8uXLmfafPXvW8ucyZcro0qVL+uGHHyxtZ86cyfRk7Dtxc3OTyWSyqtr4/ffftWzZsrs7ATvx8/NToUKFtHXrVqv2qVOnWr1OS0vLdBtbUFCQQkNDrW4T/bsWLVooLS1NU6ZMsWofP368TCaTTddfDggIUEREhBYtWqSFCxfK09NTrVu3tupz/vx5q9e+vr4qW7bsHc/hdjKqt7/++mvt37/fal/Tpk3l6empSZMmWVXKzJo1S5cuXVJkZGSOx7OVFi1a6MaNG5o2bZqlLS0tTZMnT7bZGG5ubmratKmWLVtmta7kL7/8kmltSAAAkPeRS2eNXDp7uXT37t1VpEgR9e/fXz///HOm/QkJCXr33Xcl/a/i+dYcOiUlJdM1vZN7yXdbtGghSZkmdceNGydJNsvjg4KC9Pjjj+vjjz/WmTNnMu2/9e9URESEYmNjrb5zXLhwwWp9bMk2165jx446fPiwBgwYIDc3N7Vr1y7b7wWA26FyG7ABs9msmTNnqnnz5qpSpYpefvllFS1aVKdOndKmTZvk5+enFStWSLp5O9agQYP0zDPP6LXXXtPVq1c1bdo0lS9fPtsPsImMjNS4ceP05JNP6sUXX1RCQoI++ugjlS1b1irRz6mff/5Z8+bNy9QeHBysJ5544q6O2aVLF33wwQfq0qWLHnroIW3dujVT0vnXX3+pWLFiatu2rWrUqCFfX1+tX79eu3fv1tixY2977KefflqNGjXSv//9b/3++++qUaOG1q5dq6+//lp9+vSxeuCNLfzrX/9Shw4dNHXqVEVERGSqqK5cubIef/xx1a5dWwUKFNCePXv05ZdfqlevXnc1Xsba2wcOHFD+/Pkt7YULF9bgwYM1YsQIPfnkk2rZsqWOHj2qqVOn6uGHH1aHDh3u5TTvydNPP6169erpzTff1O+//67KlStryZIl/7gGX04NHz5ca9euVb169dSjRw/LF7OqVatm+mUAAADI28ilb49c+p9z6cDAQC1dulQtWrRQzZo11aFDB9WuXVuS9P333+vzzz+3rCn96KOPKjAwUFFRUXrttddkMpn0n//8J0fL391LvlujRg1FRUVpxowZlmU+du3apblz56p169Y2ffjoRx99pMcee0zVqlVT165dVbp0acXHxys2NlZ//vmnDhw4IEkaOHCg5s2bpyeeeEK9e/dW/vz5NXPmTJUoUUIXLlywLKFii2sXGRmpggULavHixWrevLmCgoJsdr4AXJgBwGL27NmGJGP37t137Ldp0yZDkrFp0yar9n379hnPPvusUbBgQcPLy8soWbKk8fzzzxsbNmyw6rd27VqjatWqhqenp1GhQgVj3rx5xrBhw4y//5WUZERHR2cZw6xZs4xy5coZXl5eRsWKFY3Zs2dneYySJUsaUVFR/3jukm67NWzY0NIvY4yzZ89avT8qKsrInz9/puNevXrV6Ny5s+Hv72888MADxvPPP28kJCQYkoxhw4YZhmEYycnJxoABA4waNWoYDzzwgJE/f36jRo0axtSpUzONUbJkSau2v/76y+jbt68RGhpqeHh4GOXKlTPGjBljpKenZzq/rK5ldq+PYRhGUlKS4ePjY0gy5s2bl2n/u+++azzyyCNGQECA4ePjY1SsWNF47733jJSUlDseN+PztHjx4kz7Mq53Vtd2ypQpRsWKFQ0PDw8jODjY6NGjh3Hx4sV/PI9/+pwfP37ckGTMnj3b0na7n29Wn7nz588bHTt2NPz8/Ax/f3+jY8eOxr59+zIdMyef+ax+Ths2bDAefPBBw9PT0yhTpowxc+ZMo3///oa3t/c/XAEAAGAP5NLk0ndir1w6w+nTp42+ffsa5cuXN7y9vY18+fIZtWvXNt577z3j0qVLln7fffedUbduXcPHx8cIDQ01Bg4caKxZsybTZ7Jhw4ZGlSpVshzrXvLd1NRUY8SIEUZYWJjh4eFhFC9e3Bg8eLBx/fp1q34lS5Y0IiMjM42d1c8iI38fM2aMVfuvv/5qvPTSS0ZISIjh4eFhFC1a1HjqqaeML7/80qrfvn37jPr16xteXl5GsWLFjJiYGGPSpEmGJCMuLi7H1y6rz1qGnj17GpKMBQsWZLkfAHLKZBg8oQsAcH9o3bq1Dh06pGPHjuV2KAAAAIDT6tOnjz7++GNdvnzZpg+W79u3r2bNmqW4uDjly5fPZscF4LpYcxsA4JSuXbtm9frYsWP65ptv9Pjjj+dOQAAAAIAT+nteff78ef3nP//RY489ZtOJ7evXr2vevHlq06YNE9sAbIY1twEATql06dLq1KmTSpcurRMnTmjatGny9PTUwIEDczs0AAAAwGmEh4fr8ccfV6VKlRQfH69Zs2YpKSlJb7/9tk2On5CQoPXr1+vLL7/U+fPn9frrr9vkuAAgMbkNAHBSTz75pD7//HPFxcXJy8tL4eHhev/991WuXLncDg0AAABwGi1atNCXX36pGTNmyGQyqVatWpo1a5YaNGhgk+MfPnxY7du3V1BQkCZNmqSaNWva5LgAIEmsuQ0AAAAAAAAAcDqsuQ0AAAAAAAAAcDpMbgMAAAAAAAAAnA6T2wAAAAAAAAAAp5NnHih5NYWlvwHYX8E6vXM7BAAu4tq+KbkdghWfB3s5ZJy8dt64s7Rl/LwA2J+pXI3cDgGAizBXqZ/bIVjpbvJzyDjTjSSHjJMXUbkNAAAAAAAAAHA6eaZyGwAAAHZkoqYBAAAAcCQycPvjGgMAAAAAAAAAnA6V2wAAAK7AZMrtCAAAAACXYiYHtzsqtwEAAAAAAAAATofKbQAAAFfAmtsAAACAQ5GB2x/XGAAAAAAAAADgdJjcBgAAAAAAAAA4HZYlAQAAcAU8zAYAAABwKDMpuN1RuQ0AAAAAAAAAcDpUbgMAALgCHigJAAAAOBQZuP1xjQEAAAAAAAAATofKbQAAAFfAmtsAAACAQ5nJwe2Oym0AAAAAAAAAgNOhchsAAMAVsOY2AAAA4FBk4PbHNQYAAAAAAAAAOB0qtwEAAFwB6/0BAAAADmUmBbc7KrcBAAAAAAAAAE6Hym0AAABXwJrbAAAAgEORgdsf1xgAAAAAAAAA4HSo3AYAAHAFrLkNAAAAOJSJHNzuqNwGAAAAAAAAADgdKrcBAABcAWtuAwAAAA5FBm5/XGMAAAAAAAAAgNOhchsAAMAVsN4fAAAA4FBmUnC7o3IbAAAAAAAAAFzA1q1b9fTTTys0NFQmk0nLli2z2m8YhoYOHaoiRYrIx8dHTZs21bFjx6z6XLhwQe3bt5efn58CAgLUuXNnXb582arPDz/8oPr168vb21vFixfX6NGjM8WyePFiVaxYUd7e3qpWrZq++eabHJ8Pk9sAAACuwGR2zAYAAABA0s2JV0dsOXHlyhXVqFFDH330UZb7R48erUmTJmn69OnauXOn8ufPr4iICF2/ft3Sp3379jp06JDWrVunlStXauvWrerWrZtlf1JSkpo1a6aSJUtq7969GjNmjIYPH64ZM2ZY+mzfvl0vvPCCOnfurH379ql169Zq3bq1fvzxxxydj8kwDCOH18AurqbkiTAA3OcK1umd2yEAcBHX9k3J7RCs+DQY7pBxrm11zDiwjbRleetzCuD+ZCpXI7dDAOAizFXq53YIVkZ4BTpknGHJF+/qfSaTSUuXLlXr1q0l3azaDg0NVf/+/fXGG29Iki5duqTg4GDNmTNH7dq1008//aTKlStr9+7deuihhyRJq1evVosWLfTnn38qNDRU06ZN07///W/FxcXJ09NTkvTmm29q2bJlOnLkiCTpX//6l65cuaKVK1da4qlbt65q1qyp6dOnZ/scKK8BAABwBVRuAwAAAA5lNpkcsiUnJyspKclqS05OznG8x48fV1xcnJo2bWpp8/f3V506dRQbGytJio2NVUBAgGViW5KaNm0qs9msnTt3Wvo0aNDAMrEtSRERETp69KguXrxo6XPrOBl9MsbJLr6BAAAAAAAAAICTiomJkb+/v9UWExOT4+PExcVJkoKDg63ag4ODLfvi4uIUFBRktd/d3V0FChSw6pPVMW4d43Z9MvZnl3uOegMAAMA58ah2AAAAwKEcVVU8cPBg9evXz6rNy8vLQaPnLia3AQAAAAAAAMBJeXl52WQyOyQkRJIUHx+vIkWKWNrj4+NVs2ZNS5+EhASr9924cUMXLlywvD8kJETx8fFWfTJe/1OfjP3ZxbIkAAAAroA1twEAAACHMpscs9lKWFiYQkJCtGHDBktbUlKSdu7cqfDwcElSeHi4EhMTtXfvXkufjRs3Kj09XXXq1LH02bp1q1JTUy191q1bpwoVKigwMNDS59ZxMvpkjJNdfAMBAAAAAAAAABdw+fJl7d+/X/v375d08yGS+/fv18mTJ2UymdSnTx+9++67Wr58uQ4ePKiXXnpJoaGhat26tSSpUqVKevLJJ9W1a1ft2rVL3333nXr16qV27dopNDRUkvTiiy/K09NTnTt31qFDh/TFF19o4sSJVkunvP7661q9erXGjh2rI0eOaPjw4dqzZ4969eqVo/NhWRIAAABXYGLNbQAAAMCR8mJV8Z49e9SoUSPL64wJ56ioKM2ZM0cDBw7UlStX1K1bNyUmJuqxxx7T6tWr5e3tbXnP/Pnz1atXLzVp0kRms1lt2rTRpEmTLPv9/f21du1aRUdHq3bt2ipUqJCGDh2qbt26Wfo8+uijWrBggYYMGaK33npL5cqV07Jly1S1atUcnY/JMAzjbi+GLV1NyRNhALjPFazTO7dDAOAiru2bktshWPFp8r5Dxrm24S2HjAPbSFuWtz6nAO5PpnI1cjsEAC7CXKV+bodgZZRPAYeMM+jaBYeMkxdRuQ0AAOAKWA8bAAAAcCizuHvS3viWAwAAAAAAAABwOlRuAwAAuALW3AYAAAAcykwKbndUbgMAAAAAAAAAnA6V2wAAAK6ANbcBAAAAhyIDtz+uMQAAAAAAAADA6VC5DQAA4ApYcxsAAABwKNbctj8qtwEAAAAAAAAATofKbQAAAFfAmtsAAACAQ5lF6ba98S0HAAAAAAAAAOB0qNwGAABwBay5DQAAADgUa27bH5XbAAAAAAAAAACnQ+U2AACAK2DNbQAAAMChyMDtj2sMAAAAAAAAAHA6VG4DAAC4AtbcBgAAAByKNbftj8ptAAAAAAAAAIDTYXIbAAAAAAAAAOB0WJYEAADAFfBASQAAAMChzGJdEnvjWw4AAAAAAAAAwOlQuQ0AAOAKqNwGAAAAHIoHStof33IAAACQK0qVKiWTyZRpi46OliRdv35d0dHRKliwoHx9fdWmTRvFx8dbHePkyZOKjIxUvnz5FBQUpAEDBujGjRtWfTZv3qxatWrJy8tLZcuW1Zw5cxx1igAAAADsiMltAAAAV2AyOWbLgd27d+vMmTOWbd26dZKk5557TpLUt29frVixQosXL9aWLVt0+vRpPfvss5b3p6WlKTIyUikpKdq+fbvmzp2rOXPmaOjQoZY+x48fV2RkpBo1aqT9+/erT58+6tKli9asWWODiwoAAADcnslBmytjWRIAAADkisKFC1u9/uCDD1SmTBk1bNhQly5d0qxZs7RgwQI1btxYkjR79mxVqlRJO3bsUN26dbV27VodPnxY69evV3BwsGrWrKl33nlHgwYN0vDhw+Xp6anp06crLCxMY8eOlSRVqlRJ27Zt0/jx4xUREeHwcwYAAABgO1RuAwAAuAKT2SFbcnKykpKSrLbk5OR/DC8lJUXz5s3TK6+8IpPJpL179yo1NVVNmza19KlYsaJKlCih2NhYSVJsbKyqVaum4OBgS5+IiAglJSXp0KFDlj63HiOjT8YxAAAAAHsxmxyzuTImtwEAAGAzMTEx8vf3t9piYmL+8X3Lli1TYmKiOnXqJEmKi4uTp6enAgICrPoFBwcrLi7O0ufWie2M/Rn77tQnKSlJ165du5tTBAAAAJBHsCwJAACAK8jheth3a/DgwerXr59Vm5eX1z++b9asWWrevLlCQ0PtFRoAAADgUGaXXxHb/pjcBgAAgM14eXllazL7VidOnND69eu1ZMkSS1tISIhSUlKUmJhoVb0dHx+vkJAQS59du3ZZHSs+Pt6yL+P/M9pu7ePn5ycfH58cxQkAAAAgb2FZEgAAAFfgoDW378bs2bMVFBSkyMhIS1vt2rXl4eGhDRs2WNqOHj2qkydPKjw8XJIUHh6ugwcPKiEhwdJn3bp18vPzU+XKlS19bj1GRp+MYwAAAAD2wprb9sfkNgAAAHJNenq6Zs+eraioKLm7/++mQn9/f3Xu3Fn9+vXTpk2btHfvXr388ssKDw9X3bp1JUnNmjVT5cqV1bFjRx04cEBr1qzRkCFDFB0dbake7969u3777TcNHDhQR44c0dSpU7Vo0SL17ds3V84XAAAAgO2wLAkAAIArcNCa2zm1fv16nTx5Uq+88kqmfePHj5fZbFabNm2UnJysiIgITZ061bLfzc1NK1euVI8ePRQeHq78+fMrKipKI0eOtPQJCwvTqlWr1LdvX02cOFHFihXTzJkzFRER4ZDzAwAAgOuiqtj+TIZhGLkdhCRdTckTYQC4zxWs0zu3QwDgIq7tm5LbIVjxeXaWQ8a5tqSzQ8aBbaQty1ufUwD3J1O5GrkdAgAXYa5SP7dDsLIgIMgh47yYmPDPne5TVG4DAAC4AFMerdwGAAAA7ldk4PZHdTwAAAAAAAAAwOlQuQ0AAOACqNwGAAAAHMtMDm53VG4DAAAAAAAAAJwOldsAAACugKIRAAAAwKFIwe2Pym0AAAAAAAAAgNOhchsAAMAFsOY2AAAA4Fhk4PZH5TYAAAAAAAAAwOnc0+T2n3/+qfT0dFvFAgAAADsxmUwO2WB/5OAAAADOweSgzZXd0+R25cqV9fvvv9soFAAAAAD/hBwcAAAAuOme1tw2DMNWcQAAAMCOqKq+f5CDAwAAOAdycPtjzW0AAAAAAAAAgNO5p8rtt956SwUKFLBVLAAAALATqkbuH+TgAAAAzoEM3P7uaXJ78ODBtooDAAAAQDaQgwMAAAA35XhyOy0tTXPmzNGGDRuUkJCQ6UntGzdutFlwAAAAsBHKRpwaOTgAAIDzYT1o+8vx5Pbrr7+uOXPmKDIyUlWrVuUWVwAAAMDOyMEBAACAzHI8ub1w4UItWrRILVq0sEc8AAAAsAMmQ50bOTgAAIDzIQW3vxxXx3t6eqps2bL2iAUAAABAFsjBAQAAgMxyPLndv39/TZw4UYZh2CMeAAAA2IHJZHLIBvsgBwcAAHA+Jgf9z5XleFmSbdu2adOmTfr2229VpUoVeXh4WO1fsmSJzYIDAAAAQA4OAAAAZCXHk9sBAQF65pln7BELAAAA7ISqaudGDg4AAOB8yMDtL8eT27Nnz7ZHHAAAAABugxwcAAAAyCzHk9sZzp49q6NHj0qSKlSooMKFC9ssKAAAANgWldv3B3JwAAAA50EGbn85fqDklStX9Morr6hIkSJq0KCBGjRooNDQUHXu3FlXr161R4wAAACASyMHBwAAADLL8eR2v379tGXLFq1YsUKJiYlKTEzU119/rS1btqh///72iBEAAAD3yuSgDXZBDg4AAOB8zCbHbK4sx8uSfPXVV/ryyy/1+OOPW9patGghHx8fPf/885o2bZot4wMAAABcHjk4AAAAkFmOJ7evXr2q4ODgTO1BQUHcEgkAAJBHsea2cyMHBwAAcD4mbm20uxwvSxIeHq5hw4bp+vXrlrZr165pxIgRCg8Pt2lwAAAAAMjBAQAAgKzkuHJ74sSJioiIULFixVSjRg1J0oEDB+Tt7a01a9bYPEAAAADA1ZGDAwAAAJnleHK7atWqOnbsmObPn68jR45Ikl544QW1b99ePj4+Ng8QAAAA945lSZwbOTgAAIDzIQO3vxxPbktSvnz51LVrV1vHAgAAAOA2yMEBAAAAa9ma3F6+fLmaN28uDw8PLV++/I59W7ZsaZPAAAAAYDtUbjsfcnAAAADnRgpuf9ma3G7durXi4uIUFBSk1q1b37afyWRSWlqarWIDAAAAXBY5OAAAAHBn2ZrcTk9Pz/LPAAAAcBJUjTgdcnAAAADnRgpuf2ZbHCQxMdEWhwEAAACQTeTgAAAAcHU5ntweNWqUvvjiC8vr5557TgUKFFDRokV14MABmwYHAAAA2zCZTA7ZYB/k4AAAAM7HLJNDNleW48nt6dOnq3jx4pKkdevWaf369Vq9erWaN2+uAQMG2DxAAAAAwNWRgwMAAACZZWvN7VvFxcVZEuuVK1fq+eefV7NmzVSqVCnVqVPH5gECAADg3lFV7dzIwQEAAJwPGbj95bhyOzAwUH/88YckafXq1WratKkkyTAMntIOAAAA2AE5OAAAAJBZjiu3n332Wb344osqV66czp8/r+bNm0uS9u3bp7Jly9o8QAAAANw7KredGzk4AACA8yEFt78cT26PHz9epUqV0h9//KHRo0fL19dXknTmzBn17NnT5gECAAAAro4cHAAAAMjMZBiGkdtBSNLVlDwRBoD7XME6vXM7BAAu4tq+KbkdgpXQV5c4ZJzTHz/rkHFgG2nL8tbnFMD9yVSuRm6HAMBFmKvUz+0QrGwKKuqQcRolnHLIOHlRjiu3P/vsszvuf+mll+46GAAAAACZkYMDAAAAmeV4cvv111+3ep2amqqrV6/K09NT+fLlI7EGAADIi1jvz6mRgwMAADgfE0m43Zlz+oaLFy9abZcvX9bRo0f12GOP6fPPP7dHjAAAAIBLIwcHAAAAMstx5XZWypUrpw8++EAdOnTQkSNHbHFIAAAA2JCJR7Xfd8jBAQAA8jYzKbjd5bhy+3bc3d11+vRpWx0OAAAAwD8gBwcAAIAry3Hl9vLly61eG4ahM2fOaMqUKapXr57NAgMAAIDtULnt3MjBAQAAnA8ZuP3leHK7devWVq9NJpMKFy6sxo0ba+zYsbaKCwAAAMD/IwcHAAAAMsvW5HZSUpL8/PwkSenp6XYNCAAAALZH5bbzIQcHAABwbmTg9petNbcDAwOVkJAgSWrcuLESExPtGRMAAADg8sjBAQAAgDvL1uS2r6+vzp8/L0navHmzUlNT7RoUAAAAbMzkoA02Qw4OAADg3EwO+p8ry9ayJE2bNlWjRo1UqVIlSdIzzzwjT0/PLPtu3LjRdtEBAAAALoocHAAAALizbE1uz5s3T3PnztWvv/6qLVu2qEqVKsqXL5+9YwMAAICNsOa28yEHBwAAcG6k4PaXrcnt1NRUde/eXZK0Z88ejRo1SgEBAfaMCwAAAHBp5OAAAADAneX4gZJU/QAAADgfk8nkkC2nTp06pQ4dOqhgwYLy8fFRtWrVtGfPHst+wzA0dOhQFSlSRD4+PmratKmOHTtmdYwLFy6offv28vPzU0BAgDp37qzLly9b9fnhhx9Uv359eXt7q3jx4ho9evTdXUgHIgcHAABwbmYHba4sxw+U3LJlCw+zAQAAwD27ePGi6tWrJw8PD3377bc6fPiwxo4dq8DAQEuf0aNHa9KkSZo+fbp27typ/PnzKyIiQtevX7f0ad++vQ4dOqR169Zp5cqV2rp1q7p162bZn5SUpGbNmqlkyZLau3evxowZo+HDh2vGjBkOPd+cIgcHAAAA7izHD5Q0DIOH2cBu9u7Zrc/mzNLhw4d07uxZjZswRY2aNLXs37B+rb5ctFA/HT6kS5cuaeHipapQsZJl/6VLiZr20WTtiP1OcWfOKDCwgB5v3EQ9e72uBx54IDdOCUAecGTVCJUMLZipffoXW9X3g0UKK1ZIH/R9RuEPlpaXh7vWbf9J/UYtVsKFvyx9B3aOUPP6VVS9fDGl3LihIg0GWh2rw9N19MnIjlmOX6Lxmzp78XKW+wBHyYuVv6NGjVLx4sU1e/ZsS1tYWJjlz4ZhaMKECRoyZIhatWolSfrss88UHBysZcuWqV27dvrpp5+0evVq7d69Ww899JAkafLkyWrRooU+/PBDhYaGav78+UpJSdGnn34qT09PValSRfv379e4ceOsJsHzGnJw2MPC2INauOOgTl1MkiSVDS6oHk0eVoOKpSRJJ89f0phV2/T976eVciNNj5UvqX+3aqhCD/xvvffoOSv10+mzunDlmvx8vBRetrj6t3hUQX6+lj7fHjimGZv26MS5RAXm99GLj1ZX54a1HHquAHLXjK++0bod3+u3U2fk7empByuWUf+ObRVWNMTS5+zFSxrz2WLFHjisK9euq1RoiLq3jVSz8NqWPj3fn6wjv/+h85eS5Jc/v8JrVNIbHdsqqECAJGnXj0c0d8V6/XDsuK5cu6aSRYL1SqsIPd2wrqNPGcgk72Xg9x8eKIk85dq1aypfvqJaPdNG/fv0znJ/zQdr64mI5npn+NuZ9p9NSNDZswnq23+gSpcpqzOnT+u9d4bp7NkEfThukiNOAUAe9FiHMXIz/y+tqFw2VN9M760l6/Ypn7enVk6N1sGfT6l5t8mSpGE9I/XVxFfV4KWxMgxDkuTp4aYl6/Zp5w/HFdU6PNMYX679Xuu2H7ZqmzGio7y9PJjYhktJTk5WcnKyVZuXl5e8vLwy9V2+fLkiIiL03HPPacuWLSpatKh69uyprl27SpKOHz+uuLg4NW36v190+/v7q06dOoqNjVW7du0UGxurgIAAy8S2dHNS2Gw2a+fOnXrmmWcUGxurBg0aWE0MR0REaNSoUbp48aJVpXheQg4Oewj291Xf5o+qZKEAyTC0bO8R9fpslb56rZ2KFvBT15nLVKFIIc3u+owkadLaHYqes0KfRz8v8///W/pImaLq1ughFfLLp4RLVzRm1Tb1+c+3WhD9nCRp65HfNWjhWr3VqoHqlSuh3xIuaOhXm+Tt4ab2j9bIrVMH4GC7Dx3Vi80bqWrZUkpLS9f4+UvUecQ4rZz0jvJ538wL3pw0S39duaqPBvdS4AMPaOV/d6rv2OlaPPptVS5dQpL0SLUK6tamhQoHBijhwkWNnrtYr4+Zps9jBkuS9h35VeVLFlOXZ55UwQA/bd7zg96cPEu++X3U6CH+mwPc77I1ue3j48PDbOAQj9VvoMfqN7jt/qeevlm1dfrUn1nuL1uuvMaOn2x5Xbx4CfXq3Vf/HjxAN27ckLt7tj7yAO4z5/42ufzGy1X168mz+u/eY2pSt6JKhhZU3RdG6a8rN5c56DL0PzqzZbQef6S8Nu08Kkl6d/o3km5WaGflenKqrif/b8mAQoG+evyR8uo+Yr49TgnIMUdVbsfExGjEiBFWbcOGDdPw4cMz9f3tt980bdo09evXT2+99ZZ2796t1157TZ6enoqKilJcXJwkKTg42Op9wcHBln1xcXEKCgqy2u/u7q4CBQpY9bm1IvzWY8bFxeXZyW1ycNhDo8rWfxf6PBmuhTsO6oeTcUpIuqJTF//SV6+/IF/vm78Minn+CdUdMUM7fv1Dj5a7OdEUVf9By/uLBvqpS6Pa6v3ZKqWmpcnDzU0r9h1V4yql1a5uNUlS8YL+6tooSbM2f68Xw6vnyTtJANjeJ0P7Wr2O6f2K6r3cV4d+PaGHq5SXJO0/+quGduug6uVKS5J6PPeU5q5Yp0O//m6Z3O70dDPLMYoGFVTXZ5qr16iPlHrjhjzc3fVq20ircV56qqm+239I63d8z+Q2ch3/5tlfjtcc37RpE0k1nMpfl/9Sfl9fJrYBSJI83N3UrsXDmvt1rCTJy9NdhmEoOeWGpc/15BtKTzf0aM0ydz1O+6ce0dXrKVq6fv+9hgw4lcGDB+vSpUtW2+DBg7Psm56erlq1aun999/Xgw8+qG7duqlr166aPn26g6PO+8jBYQ9p6en6Zv/PupaSqholiyjlRppMJsnT3c3Sx8vDXWaTSd//fibLYyReva6V+47qwZJF5OF2830pN9LkdcsxJMnbw11xly7r9MW/sjoMABfw19WrkiR/3/yWtpoVyujb73Yr8a/LSk9P16ptu5SSmqpHqlbI8hiJf13Wiq079GCFMvK4w3f8y1evWY0D4P6V7cntypUr68KFC5bXPXv21Llz5yyvExISuE0Sec7Fixf1ycfT1Kbt87kdCoA8omWj6gp4wEfzVuyUJO06+LuuXEvRe6+3ko+3h/J5e+qDfs/I3d1NIYX87nqcqNbh+uLbPVbV3ECuMjlm8/Lykp+fn9WW1ZIkklSkSBFVrlzZqq1SpUo6efKkJCkk5OaanPHx8VZ94uPjLftCQkKUkJBgtf/GjRu6cOGCVZ+sjnHrGHkVOTjs4ecz51T77emq+e+pGrF0kya9FKmywQVUo0SIfDw8NPab73QtJVVXU1I1etU2paUbOpt0xeoYY7/5TrWHTNOjIz7RmcTLmvLS/yon65UvofU//qrYX/5Qerqh389e1Jyt+yRJZ/+yPg4A15Cenq6YT79QrYplVb5kUUv7+De660ZamsKj+qjGv3po+PT/aPKgaJUsYn3X1oeffalaL/RUeFQfnTl3QVMG97rtWN9+t1sHf/ldzzSuZ7fzAbLLQSm4S8v25PaRI0d048b/qtrmzZunpKQky2vDMKyeWn8nycnJSkpKstr+vjYjcK8uX76s16JfVenSZfRqj9v/wwfAtUS1flRrvjusM2cvSbq5ZEn7gbPUokFVnfturOL/O0b+vj76/vBJpf//ets5Vad6mCqVLqK5y2JtGTpw36lXr56OHj1q1fbzzz+rZMmSkm4+XDIkJEQbNmyw7E9KStLOnTsVHn5z7fvw8HAlJiZq7969lj4bN25Uenq66tSpY+mzdetWpab+75dN69atU4UKFfLskiQZ7J6Dp/ILOFdUqnCglrzeTgujn9e/6lbTW4vW6Zf4Cyrg66PxHZpr80/H9dDQ6aoz7GP9dS1ZlYsWtqy3neGVhrX01evtNLNzK7mZTXpz0TrLcyqee6SKXny0unrOXqEa//5I7T5arOY1ykni9mzAVY38ZL6OnTylsf2sH+Q8acEy/XXlqj4d3l+LRw9Rp6efUN8Pp+vnE9ZLkXZuHaGvxg7VzKF95WY2682Jsyz/zbnVzoNH9O8pszWyx0sqV6Jopv0A7j85XpYkQ1b/EcluohITEyN/f3+r7cPRMXcbCpDJlSuXFd29i/Lly69xE6fIw8Mjt0MCkAeUKBKoxnUqaM6y7VbtG3YcUZWWI1SiyWAVa/SmOr/9mUKDAvT7n+duc6Q76/RMuPYf+UP7fvrDFmEDNmEymRyy5UTfvn21Y8cOvf/++/rll1+0YMECzZgxQ9HR0ZaY+/Tpo3fffVfLly/XwYMH9dJLLyk0NFStW7eWdLPS+8knn1TXrl21a9cufffdd+rVq5fatWun0NBQSdKLL74oT09Pde7cWYcOHdIXX3yhiRMnql+/fja9xo5g6xz8g6/W2TpEOAFPdzeVLBSgKsWC1K/5o6pQpJD+s22/pJtV12sGRWnb21303dCuGtWumeIvXVGxAv5WxwjM76NShQP1aPkS+vCFJ7X1yAkdOHlznXuTyaT+Leppzzvdtf7NTto6pLOqFb9ZhVn8b8cBcP9755P52rLnB80d+YZCChWwtJ+MS9D8bzfq3ehOCq9eSRXDiiv6Xy1VpWwpLfh2k9UxAv0eUFhoiOrVrKKx/bpp6/cHtf/n36z67Dp0VD1jJuvNl/+l1o0edci5Af+Eym37y5VFiAcPHpzpy0SayfM2vYGcuXz5snq+2lmenp6aMHnqbW+FBuB6OrYMV8KFv/Ttfw9luf984s1bpRs+XF5BBXy1csvBHI+R38dTbZ6opaGTl99TrIArePjhh7V06VINHjxYI0eOVFhYmCZMmKD27dtb+gwcOFBXrlxRt27dlJiYqMcee0yrV6+Wt7e3pc/8+fPVq1cvNWnSRGazWW3atNGkSZMs+/39/bV27VpFR0erdu3aKlSokIYOHapu3ayrx+53WeXg7mtm5VI0yEsMQ0pNS7NqC8zvI0na8csfunDlqhr/7UGUt8q40ynlhvUx3MxmBfv7SpK+OfCzapYIUQFfH1uGDiAPMwxD785coPU792nuyAEqFlzYav/*********************************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", "text/plain": ["<Figure size 1500x600 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== Pleural Effusion Demographic Distribution Analysis ===\n", "Note: Based on HuggingFace page warning, assuming AP/PA mapping: 0=AP, 1=PA\n", "\n", "Distribution by View Type:\n", "AP: 68877/84696 (81.32%)\n", "PA: 8081/17694 (45.67%)\n", "LL: 4/9 (44.44%)\n", "RL: 1/1 (100.00%)\n", "\n", "Distribution by Sex:\n", "Male: 44984/59724 (75.32%)\n", "Female: 31979/42675 (74.94%)\n", "Unknown: 0/1 (0.00%)\n", "\n", "Distribution by Age:\n", "50-70: 32494/42796 (75.93%)\n", "70+: 29093/35691 (81.51%)\n", "30-50: 11765/17742 (66.31%)\n", "<30: 3611/6171 (58.52%)\n", "All samples age: 61.5 ± 17.5\n", "Positive samples age: 63.1 ± 16.9\n"]}, {"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== Pleural Effusion Class Priors and Baseline Analysis ===\n", "Total valid samples: 102,400\n", "Positive samples: 76,963 (prior: 0.7516)\n", "Negative samples: 25,437 (prior: 0.2484)\n", "Majority class: Positive\n", "Majority class baseline accuracy: 0.7516 (75.16%)\n", "Majority class baseline error rate: 0.2484 (24.84%)\n", "Class imbalance ratio: 3.03:1\n"]}], "source": ["analyzer = CheXpertAnalyzer(train_df=train_df, val_df=val_df, target_pathology=\"Pleural Effusion\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 2}